# Deployment Guide

This guide covers the deployment process and security hardening for the Appraisal Tool application.

## Pre-Deployment Checklist

### 🔒 Security
- [ ] All environment variables are properly configured
- [ ] HTTPS is enforced in production
- [ ] Security headers are enabled
- [ ] Rate limiting is configured
- [ ] CSRF protection is enabled
- [ ] Input validation is implemented
- [ ] SQL injection protection is in place
- [ ] XSS protection is enabled

### 🚀 Performance
- [ ] Bundle optimization is enabled
- [ ] Code splitting is configured
- [ ] Images are optimized
- [ ] Caching strategies are implemented
- [ ] Compression is enabled
- [ ] Performance monitoring is set up

### ♿ Accessibility
- [ ] All components have proper ARIA labels
- [ ] Keyboard navigation works correctly
- [ ] Screen reader support is implemented
- [ ] Color contrast meets WCAG standards
- [ ] Focus management is proper
- [ ] Skip links are available

### 🧪 Testing
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Accessibility tests pass
- [ ] Performance tests pass
- [ ] Security tests pass

### 📊 Monitoring
- [ ] Error tracking is configured
- [ ] Performance monitoring is set up
- [ ] Logging is properly configured
- [ ] Health checks are implemented
- [ ] Alerts are configured

## Environment Variables

### Required for All Environments

```bash
# Database
DATABASE_URL=postgresql://username:password@host:port/database

# Authentication
CLERK_SECRET_KEY=sk_test_...
CLERK_PUBLISHABLE_KEY=pk_test_...
SESSION_SECRET=your-32-character-session-secret

# Security
CSRF_SECRET=your-32-character-csrf-secret
```

### Development Environment

```bash
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000
LOG_LEVEL=debug
ENABLE_CONSOLE_LOGGING=true
RATE_LIMIT_MAX_REQUESTS=1000
RATE_LIMIT_WINDOW_MS=60000
```

### Staging Environment

```bash
NODE_ENV=staging
NEXT_PUBLIC_APP_URL=https://staging.your-domain.com
LOG_LEVEL=info
ENABLE_CONSOLE_LOGGING=true
RATE_LIMIT_MAX_REQUESTS=200
RATE_LIMIT_WINDOW_MS=60000
SENTRY_DSN=https://<EMAIL>/project-id
```

### Production Environment

```bash
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://your-domain.com
LOG_LEVEL=warn
ENABLE_CONSOLE_LOGGING=false
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_WINDOW_MS=60000
SENTRY_DSN=https://<EMAIL>/project-id
ANALYTICS_ID=your-analytics-id
```

## Deployment Steps

### 1. Prepare the Application

```bash
# Install dependencies
npm install --production

# Run tests
npm test

# Build the application
npm run build

# Verify the build
npm run start
```

### 2. Database Setup

```bash
# Run database migrations
npm run db:migrate

# Seed initial data (if needed)
npm run db:seed
```

### 3. Security Hardening

#### Enable Security Headers
The application automatically sets security headers in production:
- `X-Frame-Options: DENY`
- `X-Content-Type-Options: nosniff`
- `X-XSS-Protection: 1; mode=block`
- `Strict-Transport-Security: max-age=31536000; includeSubDomains; preload`
- `Referrer-Policy: strict-origin-when-cross-origin`

#### Configure Rate Limiting
Rate limiting is automatically enabled in production with the following limits:
- API endpoints: 100 requests per 15 minutes
- Authentication: 5 attempts per 15 minutes
- Form submissions: 10 requests per minute
- File uploads: 5 requests per minute

#### Enable CSRF Protection
CSRF protection is automatically enabled in production environments.

### 4. Performance Optimization

#### Bundle Analysis
```bash
# Analyze bundle size
npm run build:analyze
```

#### Caching Strategy
- Static assets: 1 year cache
- API responses: 5 minutes cache
- Database queries: 10 minutes cache

### 5. Monitoring Setup

#### Error Tracking
Configure Sentry for error tracking:
```bash
SENTRY_DSN=https://<EMAIL>/project-id
```

#### Performance Monitoring
The application includes built-in performance monitoring that tracks:
- Page load times
- API response times
- Memory usage
- Bundle sizes

#### Health Checks
The application provides health check endpoints:
- `/api/health` - Basic health check
- `/api/health/detailed` - Detailed system status

## Deployment Platforms

### Vercel (Recommended)

1. Connect your GitHub repository to Vercel
2. Configure environment variables in the Vercel dashboard
3. Deploy automatically on push to main branch

```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel --prod
```

### Docker Deployment

```dockerfile
# Dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json package-lock.json ./
RUN npm ci --only=production

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

RUN npm run build

# Production image
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000

CMD ["node", "server.js"]
```

### Traditional Server Deployment

```bash
# Install PM2 for process management
npm install -g pm2

# Start the application
pm2 start npm --name "appraisal-tool" -- start

# Save PM2 configuration
pm2 save

# Setup PM2 to start on boot
pm2 startup
```

## Post-Deployment Verification

### 1. Functional Testing
- [ ] User authentication works
- [ ] All forms submit correctly
- [ ] Data displays properly
- [ ] File uploads work
- [ ] Email notifications send

### 2. Security Testing
- [ ] HTTPS redirects work
- [ ] Security headers are present
- [ ] Rate limiting is active
- [ ] CSRF protection works
- [ ] Input validation prevents attacks

### 3. Performance Testing
- [ ] Page load times are acceptable
- [ ] API responses are fast
- [ ] Images load quickly
- [ ] Bundle sizes are optimized

### 4. Accessibility Testing
- [ ] Screen readers work correctly
- [ ] Keyboard navigation functions
- [ ] Color contrast is sufficient
- [ ] Focus indicators are visible

## Maintenance

### Regular Tasks
- [ ] Update dependencies monthly
- [ ] Review security logs weekly
- [ ] Monitor performance metrics daily
- [ ] Backup database daily
- [ ] Review error logs daily

### Security Updates
- [ ] Apply security patches immediately
- [ ] Update Node.js regularly
- [ ] Review and rotate secrets quarterly
- [ ] Audit dependencies for vulnerabilities

### Performance Monitoring
- [ ] Monitor Core Web Vitals
- [ ] Track error rates
- [ ] Monitor memory usage
- [ ] Review slow queries

## Troubleshooting

### Common Issues

#### Build Failures
```bash
# Clear cache and rebuild
rm -rf .next node_modules
npm install
npm run build
```

#### Database Connection Issues
```bash
# Test database connection
npm run db:test-connection
```

#### Performance Issues
```bash
# Analyze bundle
npm run build:analyze

# Check memory usage
npm run monitor:memory
```

## Support

For deployment support:
1. Check the logs first
2. Review the troubleshooting section
3. Contact the development team
4. Create an issue in the repository

## Security Contacts

For security issues:
- Email: <EMAIL>
- Create a private security issue
- Follow responsible disclosure practices
