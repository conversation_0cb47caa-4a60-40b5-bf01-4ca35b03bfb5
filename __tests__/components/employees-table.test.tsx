/**
 * @jest-environment jsdom
 */

import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { EmployeesTable } from '@/components/employees-table'
import type { Employee, Department } from '@/lib/types'

// Mock the data module
jest.mock('@/lib/data', () => ({
  getManagers: jest.fn(() => Promise.resolve([
    { id: 'mgr1', fullName: 'Manager One', role: 'manager' },
    { id: 'mgr2', fullName: 'Manager Two', role: 'manager' },
  ])),
}))

// Mock the accessibility module
jest.mock('@/lib/accessibility', () => ({
  announceToScreenReader: jest.fn(),
  getStatusAnnouncement: jest.fn((status) => `Status: ${status}`),
  getTableAnnouncement: jest.fn((rows, cols) => `Table with ${rows} rows and ${cols} columns`),
  getSortAnnouncement: jest.fn((column, direction) => `${column} sorted ${direction || 'none'}`),
}))

// Mock the form dialog
jest.mock('@/components/employee-form-dialog', () => ({
  EmployeeFormDialog: ({ isOpen, onClose, employee }: any) => (
    isOpen ? (
      <div data-testid="employee-form-dialog">
        <h2>{employee ? 'Edit Employee' : 'Add New Employee'}</h2>
        <button onClick={onClose}>Close</button>
      </div>
    ) : null
  ),
}))

describe('EmployeesTable', () => {
  const user = userEvent.setup()

  const mockEmployees: Employee[] = [
    {
      id: 'emp1',
      fullName: 'John Doe',
      departmentId: 'dept1',
      departmentName: 'Engineering',
      managerId: 'mgr1',
      managerName: 'Manager One',
      compensation: 'monthly',
      rate: 5000,
      active: true,
    },
    {
      id: 'emp2',
      fullName: 'Jane Smith',
      departmentId: 'dept2',
      departmentName: 'Marketing',
      managerId: 'mgr2',
      managerName: 'Manager Two',
      compensation: 'hourly',
      rate: 25,
      active: false,
    },
  ]

  const mockDepartments: Department[] = [
    { id: 'dept1', name: 'Engineering' },
    { id: 'dept2', name: 'Marketing' },
  ]

  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('rendering', () => {
    it('should render the table with employee data', async () => {
      render(<EmployeesTable data={mockEmployees} departments={mockDepartments} />)
      
      // Check table headers
      expect(screen.getByText('Name')).toBeInTheDocument()
      expect(screen.getByText('Department')).toBeInTheDocument()
      expect(screen.getByText('Manager')).toBeInTheDocument()
      expect(screen.getByText('Compensation')).toBeInTheDocument()
      expect(screen.getByText('Rate')).toBeInTheDocument()
      expect(screen.getByText('Status')).toBeInTheDocument()
      
      // Check employee data
      expect(screen.getByText('John Doe')).toBeInTheDocument()
      expect(screen.getByText('Jane Smith')).toBeInTheDocument()
      expect(screen.getByText('Engineering')).toBeInTheDocument()
      expect(screen.getByText('Marketing')).toBeInTheDocument()
    })

    it('should display compensation rates correctly', () => {
      render(<EmployeesTable data={mockEmployees} departments={mockDepartments} />)
      
      // Monthly rate should not have "/ hr"
      expect(screen.getByText('$5,000.00')).toBeInTheDocument()
      
      // Hourly rate should have "/ hr"
      expect(screen.getByText('$25.00 / hr')).toBeInTheDocument()
    })

    it('should display status badges correctly', () => {
      render(<EmployeesTable data={mockEmployees} departments={mockDepartments} />)
      
      expect(screen.getByText('Active')).toBeInTheDocument()
      expect(screen.getByText('Inactive')).toBeInTheDocument()
    })
  })

  describe('filtering', () => {
    it('should filter employees by name', async () => {
      render(<EmployeesTable data={mockEmployees} departments={mockDepartments} />)
      
      const filterInput = screen.getByPlaceholderText('Filter by name...')
      await user.type(filterInput, 'John')
      
      // Should show John Doe but not Jane Smith
      expect(screen.getByText('John Doe')).toBeInTheDocument()
      expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument()
    })

    it('should announce filter changes to screen readers', async () => {
      const { announceToScreenReader } = require('@/lib/accessibility')
      
      render(<EmployeesTable data={mockEmployees} departments={mockDepartments} />)
      
      const filterInput = screen.getByPlaceholderText('Filter by name...')
      await user.type(filterInput, 'John')
      
      expect(announceToScreenReader).toHaveBeenCalledWith(
        'Filtering employees by name: John'
      )
    })

    it('should clear filter and announce when input is cleared', async () => {
      const { announceToScreenReader } = require('@/lib/accessibility')
      
      render(<EmployeesTable data={mockEmployees} departments={mockDepartments} />)
      
      const filterInput = screen.getByPlaceholderText('Filter by name...')
      await user.type(filterInput, 'John')
      await user.clear(filterInput)
      
      expect(announceToScreenReader).toHaveBeenCalledWith(
        'Filter cleared, showing all employees'
      )
    })
  })

  describe('sorting', () => {
    it('should have sortable column headers', () => {
      render(<EmployeesTable data={mockEmployees} departments={mockDepartments} />)
      
      // Name and Department columns should be sortable (have buttons)
      const nameHeader = screen.getByRole('button', { name: /Name/ })
      const departmentHeader = screen.getByRole('button', { name: /Department/ })
      
      expect(nameHeader).toBeInTheDocument()
      expect(departmentHeader).toBeInTheDocument()
    })

    it('should announce sort changes to screen readers', async () => {
      const { announceToScreenReader } = require('@/lib/accessibility')
      
      render(<EmployeesTable data={mockEmployees} departments={mockDepartments} />)
      
      const nameHeader = screen.getByRole('button', { name: /Name/ })
      await user.click(nameHeader)
      
      expect(announceToScreenReader).toHaveBeenCalledWith(
        expect.stringContaining('Name sorted')
      )
    })
  })

  describe('actions', () => {
    it('should open add employee dialog when Add New Employee is clicked', async () => {
      render(<EmployeesTable data={mockEmployees} departments={mockDepartments} />)
      
      const addButton = screen.getByText('Add New Employee')
      await user.click(addButton)
      
      expect(screen.getByTestId('employee-form-dialog')).toBeInTheDocument()
      expect(screen.getByText('Add New Employee')).toBeInTheDocument()
    })

    it('should open edit dialog when edit action is clicked', async () => {
      render(<EmployeesTable data={mockEmployees} departments={mockDepartments} />)
      
      // Find and click the actions button for the first employee
      const actionButtons = screen.getAllByLabelText(/Actions for/)
      await user.click(actionButtons[0])
      
      // Click edit option
      const editButton = screen.getByText('Edit Employee')
      await user.click(editButton)
      
      expect(screen.getByTestId('employee-form-dialog')).toBeInTheDocument()
      expect(screen.getByText('Edit Employee')).toBeInTheDocument()
    })

    it('should announce dialog state changes', async () => {
      const { announceToScreenReader } = require('@/lib/accessibility')
      
      render(<EmployeesTable data={mockEmployees} departments={mockDepartments} />)
      
      const addButton = screen.getByText('Add New Employee')
      await user.click(addButton)
      
      expect(announceToScreenReader).toHaveBeenCalledWith(
        'Opening new employee form'
      )
    })
  })

  describe('pagination', () => {
    it('should have pagination controls', () => {
      render(<EmployeesTable data={mockEmployees} departments={mockDepartments} />)
      
      expect(screen.getByLabelText('Go to previous page')).toBeInTheDocument()
      expect(screen.getByLabelText('Go to next page')).toBeInTheDocument()
    })

    it('should announce page changes', async () => {
      const { announceToScreenReader } = require('@/lib/accessibility')
      
      // Create enough data to enable pagination
      const manyEmployees = Array.from({ length: 15 }, (_, i) => ({
        ...mockEmployees[0],
        id: `emp${i}`,
        fullName: `Employee ${i}`,
      }))
      
      render(<EmployeesTable data={manyEmployees} departments={mockDepartments} />)
      
      const nextButton = screen.getByLabelText('Go to next page')
      if (!nextButton.hasAttribute('disabled')) {
        await user.click(nextButton)
        
        expect(announceToScreenReader).toHaveBeenCalledWith('Moved to next page')
      }
    })
  })

  describe('accessibility', () => {
    it('should have proper table structure with ARIA attributes', () => {
      render(<EmployeesTable data={mockEmployees} departments={mockDepartments} />)
      
      const table = screen.getByRole('table')
      expect(table).toHaveAttribute('aria-label')
      expect(table).toHaveAttribute('role', 'table')
    })

    it('should have proper row and cell roles', () => {
      render(<EmployeesTable data={mockEmployees} departments={mockDepartments} />)
      
      const rows = screen.getAllByRole('row')
      expect(rows.length).toBeGreaterThan(0)
      
      const cells = screen.getAllByRole('gridcell')
      expect(cells.length).toBeGreaterThan(0)
    })

    it('should announce table information on mount', () => {
      const { announceToScreenReader } = require('@/lib/accessibility')
      
      render(<EmployeesTable data={mockEmployees} departments={mockDepartments} />)
      
      expect(announceToScreenReader).toHaveBeenCalledWith(
        expect.stringContaining('Table with')
      )
    })

    it('should have accessible action buttons', () => {
      render(<EmployeesTable data={mockEmployees} departments={mockDepartments} />)
      
      const actionButtons = screen.getAllByLabelText(/Actions for/)
      expect(actionButtons.length).toBe(mockEmployees.length)
      
      actionButtons.forEach((button, index) => {
        expect(button).toHaveAttribute('aria-label', `Actions for ${mockEmployees[index].fullName}`)
      })
    })
  })

  describe('empty state', () => {
    it('should show no results message when data is empty', () => {
      render(<EmployeesTable data={[]} departments={mockDepartments} />)
      
      expect(screen.getByText('No results.')).toBeInTheDocument()
    })

    it('should have accessible empty state', () => {
      render(<EmployeesTable data={[]} departments={mockDepartments} />)
      
      const emptyCell = screen.getByText('No results.')
      expect(emptyCell).toHaveAttribute('aria-label', 'No employees found')
    })
  })

  describe('integration with managers data', () => {
    it('should load managers data on mount', async () => {
      const { getManagers } = require('@/lib/data')
      
      render(<EmployeesTable data={mockEmployees} departments={mockDepartments} />)
      
      await waitFor(() => {
        expect(getManagers).toHaveBeenCalled()
      })
    })
  })
})
