/**
 * @jest-environment jsdom
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { EnhancedButton, useButtonState } from '@/components/enhanced-button'

// Mock the accessibility module
jest.mock('@/lib/accessibility', () => ({
  announceToScreenReader: jest.fn(),
}))

// Mock the transitions module
jest.mock('@/lib/transitions', () => ({
  transitions: {
    button: 'transition-all duration-150 ease-in-out hover:scale-105 active:scale-95',
  },
  getAccessibleTransition: jest.fn((transition) => `${transition} motion-reduce:transition-none`),
}))

describe('EnhancedButton', () => {
  const user = userEvent.setup()

  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('basic functionality', () => {
    it('should render with default props', () => {
      render(<EnhancedButton>Click me</EnhancedButton>)
      
      const button = screen.getByRole('button', { name: 'Click me' })
      expect(button).toBeInTheDocument()
      expect(button).not.toBeDisabled()
    })

    it('should handle click events', async () => {
      const handleClick = jest.fn()
      render(<EnhancedButton onClick={handleClick}>Click me</EnhancedButton>)
      
      const button = screen.getByRole('button')
      await user.click(button)
      
      expect(handleClick).toHaveBeenCalledTimes(1)
    })

    it('should be disabled when disabled prop is true', () => {
      render(<EnhancedButton disabled>Disabled button</EnhancedButton>)
      
      const button = screen.getByRole('button')
      expect(button).toBeDisabled()
    })
  })

  describe('loading state', () => {
    it('should show loading state when loading prop is true', () => {
      render(<EnhancedButton loading>Loading button</EnhancedButton>)
      
      const button = screen.getByRole('button')
      expect(button).toBeDisabled()
      expect(button).toHaveAttribute('aria-busy', 'true')
      
      // Should show loading spinner
      expect(screen.getByRole('button')).toHaveTextContent('Loading button')
    })

    it('should show custom loading text', () => {
      render(
        <EnhancedButton loading loadingText="Saving...">
          Save
        </EnhancedButton>
      )
      
      expect(screen.getByText('Saving...')).toBeInTheDocument()
    })

    it('should announce loading state to screen readers', () => {
      const { announceToScreenReader } = require('@/lib/accessibility')
      
      render(<EnhancedButton state="loading" loadingText="Processing">Process</EnhancedButton>)
      
      expect(announceToScreenReader).toHaveBeenCalledWith('Processing', 'polite')
    })
  })

  describe('state management', () => {
    it('should handle success state', () => {
      render(<EnhancedButton state="success" successText="Saved!">Save</EnhancedButton>)
      
      expect(screen.getByText('Saved!')).toBeInTheDocument()
    })

    it('should handle error state', () => {
      render(<EnhancedButton state="error" errorText="Failed to save">Save</EnhancedButton>)
      
      expect(screen.getByText('Failed to save')).toBeInTheDocument()
    })

    it('should call onStateChange when state changes', async () => {
      const onStateChange = jest.fn()
      const onClick = jest.fn().mockResolvedValue(undefined)
      
      render(
        <EnhancedButton onClick={onClick} onStateChange={onStateChange}>
          Click me
        </EnhancedButton>
      )
      
      const button = screen.getByRole('button')
      await user.click(button)
      
      expect(onStateChange).toHaveBeenCalledWith('loading')
      
      await waitFor(() => {
        expect(onStateChange).toHaveBeenCalledWith('success')
      })
    })
  })

  describe('accessibility features', () => {
    it('should have proper ARIA attributes', () => {
      render(<EnhancedButton loading>Loading button</EnhancedButton>)
      
      const button = screen.getByRole('button')
      expect(button).toHaveAttribute('aria-busy', 'true')
      expect(button).toHaveAttribute('aria-live', 'polite')
    })

    it('should have descriptive aria-label based on state', () => {
      render(<EnhancedButton state="loading" loadingText="Saving data">Save</EnhancedButton>)
      
      const button = screen.getByRole('button')
      expect(button).toHaveAttribute('aria-label', 'Saving data, please wait')
    })

    it('should announce state changes to screen readers', () => {
      const { announceToScreenReader } = require('@/lib/accessibility')
      
      const { rerender } = render(<EnhancedButton state="idle">Button</EnhancedButton>)
      
      rerender(<EnhancedButton state="success" successText="Success!">Button</EnhancedButton>)
      
      expect(announceToScreenReader).toHaveBeenCalledWith('Success!', 'polite')
    })
  })

  describe('visual feedback', () => {
    it('should apply pressed state on mouse down', () => {
      render(<EnhancedButton>Press me</EnhancedButton>)
      
      const button = screen.getByRole('button')
      fireEvent.mouseDown(button)
      
      // The component should apply scale-95 class when pressed
      expect(button.className).toContain('scale-95')
    })

    it('should remove pressed state on mouse up', () => {
      render(<EnhancedButton>Press me</EnhancedButton>)
      
      const button = screen.getByRole('button')
      fireEvent.mouseDown(button)
      fireEvent.mouseUp(button)
      
      // The pressed state should be removed
      expect(button.className).not.toContain('scale-95')
    })

    it('should apply success styling in success state', () => {
      render(<EnhancedButton state="success">Success button</EnhancedButton>)
      
      const button = screen.getByRole('button')
      expect(button.className).toContain('bg-green-600')
    })

    it('should apply error styling in error state', () => {
      render(<EnhancedButton state="error">Error button</EnhancedButton>)
      
      const button = screen.getByRole('button')
      expect(button.className).toContain('bg-red-600')
    })
  })

  describe('async operations', () => {
    it('should handle successful async operations', async () => {
      const asyncOperation = jest.fn().mockResolvedValue('success')
      
      render(<EnhancedButton onClick={asyncOperation}>Async button</EnhancedButton>)
      
      const button = screen.getByRole('button')
      await user.click(button)
      
      expect(asyncOperation).toHaveBeenCalled()
      
      // Should eventually show success state
      await waitFor(() => {
        expect(button).toHaveAttribute('aria-label', expect.stringContaining('success'))
      })
    })

    it('should handle failed async operations', async () => {
      const asyncOperation = jest.fn().mockRejectedValue(new Error('Operation failed'))
      
      render(<EnhancedButton onClick={asyncOperation}>Async button</EnhancedButton>)
      
      const button = screen.getByRole('button')
      await user.click(button)
      
      expect(asyncOperation).toHaveBeenCalled()
      
      // Should eventually show error state
      await waitFor(() => {
        expect(button.className).toContain('bg-red-600')
      })
    })
  })

  describe('variants and sizes', () => {
    it('should apply variant classes', () => {
      render(<EnhancedButton variant="destructive">Delete</EnhancedButton>)
      
      const button = screen.getByRole('button')
      expect(button.className).toContain('bg-destructive')
    })

    it('should apply size classes', () => {
      render(<EnhancedButton size="lg">Large button</EnhancedButton>)
      
      const button = screen.getByRole('button')
      expect(button.className).toContain('h-11')
    })
  })
})

describe('useButtonState hook', () => {
  function TestComponent() {
    const buttonState = useButtonState()
    
    return (
      <div>
        <span data-testid="state">{buttonState.state}</span>
        <button onClick={buttonState.setLoading}>Set Loading</button>
        <button onClick={buttonState.setSuccess}>Set Success</button>
        <button onClick={buttonState.setError}>Set Error</button>
        <button onClick={buttonState.reset}>Reset</button>
      </div>
    )
  }

  it('should start with idle state', () => {
    render(<TestComponent />)
    
    expect(screen.getByTestId('state')).toHaveTextContent('idle')
  })

  it('should change to loading state', async () => {
    const user = userEvent.setup()
    render(<TestComponent />)
    
    await user.click(screen.getByText('Set Loading'))
    
    expect(screen.getByTestId('state')).toHaveTextContent('loading')
  })

  it('should change to success state', async () => {
    const user = userEvent.setup()
    render(<TestComponent />)
    
    await user.click(screen.getByText('Set Success'))
    
    expect(screen.getByTestId('state')).toHaveTextContent('success')
  })

  it('should reset to idle state', async () => {
    const user = userEvent.setup()
    render(<TestComponent />)
    
    await user.click(screen.getByText('Set Loading'))
    await user.click(screen.getByText('Reset'))
    
    expect(screen.getByTestId('state')).toHaveTextContent('idle')
  })
})
