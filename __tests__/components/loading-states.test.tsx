/**
 * @jest-environment jsdom
 */

import { render, screen, waitFor } from '@testing-library/react'
import {
  TableSkeleton,
  FormSkeleton,
  CardSkeleton,
  DashboardSkeleton,
  LoadingSpinner,
  LoadingOverlay,
} from '@/components/loading-states'

// Mock the accessibility module
jest.mock('@/lib/accessibility', () => ({
  announceLoadingState: jest.fn(),
}))

// Mock the skeleton component
jest.mock('@/components/ui/skeleton', () => ({
  Skeleton: ({ className }: { className?: string }) => (
    <div data-testid="skeleton" className={className} />
  ),
}))

// Mock the card components
jest.mock('@/components/ui/card', () => ({
  Card: ({ children, ...props }: any) => <div data-testid="card" {...props}>{children}</div>,
  CardHeader: ({ children }: any) => <div data-testid="card-header">{children}</div>,
  CardContent: ({ children }: any) => <div data-testid="card-content">{children}</div>,
}))

describe('Loading States Components', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('TableSkeleton', () => {
    it('should render table skeleton structure', () => {
      render(<TableSkeleton context="employee data" />)
      
      expect(screen.getByRole('status')).toBeInTheDocument()
      expect(screen.getByLabelText('Loading employee data')).toBeInTheDocument()
      expect(screen.getByText('Loading employee data, please wait...')).toBeInTheDocument()
    })

    it('should have proper accessibility attributes', () => {
      render(<TableSkeleton context="test data" />)
      
      const container = screen.getByRole('status')
      expect(container).toHaveAttribute('aria-label', 'Loading test data')
      expect(container).toHaveAttribute('aria-live', 'polite')
    })

    it('should announce loading state on mount', () => {
      const { announceLoadingState } = require('@/lib/accessibility')
      
      render(<TableSkeleton context="employee data" />)
      
      expect(announceLoadingState).toHaveBeenCalledWith(true, 'employee data')
    })

    it('should announce loading completion on unmount', () => {
      const { announceLoadingState } = require('@/lib/accessibility')
      
      const { unmount } = render(<TableSkeleton context="employee data" />)
      unmount()
      
      expect(announceLoadingState).toHaveBeenCalledWith(false, 'employee data')
    })

    it('should render skeleton elements for table structure', () => {
      render(<TableSkeleton context="test" />)
      
      const skeletons = screen.getAllByTestId('skeleton')
      expect(skeletons.length).toBeGreaterThan(0)
    })
  })

  describe('FormSkeleton', () => {
    it('should render form skeleton structure', () => {
      render(<FormSkeleton context="user form" />)
      
      expect(screen.getByRole('status')).toBeInTheDocument()
      expect(screen.getByLabelText('Loading user form form')).toBeInTheDocument()
      expect(screen.getByTestId('card')).toBeInTheDocument()
    })

    it('should have proper accessibility attributes', () => {
      render(<FormSkeleton context="contact form" />)
      
      const container = screen.getByRole('status')
      expect(container).toHaveAttribute('aria-label', 'Loading contact form form')
      expect(container).toHaveAttribute('aria-live', 'polite')
    })

    it('should announce loading state', () => {
      const { announceLoadingState } = require('@/lib/accessibility')
      
      render(<FormSkeleton context="user form" />)
      
      expect(announceLoadingState).toHaveBeenCalledWith(true, 'user form')
    })
  })

  describe('CardSkeleton', () => {
    it('should render card skeleton structure', () => {
      render(<CardSkeleton context="user profile" />)
      
      expect(screen.getByRole('status')).toBeInTheDocument()
      expect(screen.getByLabelText('Loading user profile')).toBeInTheDocument()
      expect(screen.getByTestId('card')).toBeInTheDocument()
    })

    it('should have proper accessibility attributes', () => {
      render(<CardSkeleton context="dashboard card" />)
      
      const container = screen.getByRole('status')
      expect(container).toHaveAttribute('aria-label', 'Loading dashboard card')
      expect(container).toHaveAttribute('aria-live', 'polite')
    })
  })

  describe('DashboardSkeleton', () => {
    it('should render dashboard skeleton structure', () => {
      render(<DashboardSkeleton />)
      
      expect(screen.getByRole('status')).toBeInTheDocument()
      expect(screen.getByLabelText('Loading dashboard')).toBeInTheDocument()
    })

    it('should announce dashboard loading', () => {
      const { announceLoadingState } = require('@/lib/accessibility')
      
      render(<DashboardSkeleton />)
      
      expect(announceLoadingState).toHaveBeenCalledWith(true, 'dashboard')
    })

    it('should render multiple card skeletons', () => {
      render(<DashboardSkeleton />)
      
      const cards = screen.getAllByTestId('card')
      expect(cards.length).toBeGreaterThan(1)
    })
  })

  describe('LoadingSpinner', () => {
    it('should render with default props', () => {
      render(<LoadingSpinner />)
      
      expect(screen.getByRole('status')).toBeInTheDocument()
      expect(screen.getByLabelText('Loading')).toBeInTheDocument()
      expect(screen.getByText('Loading, please wait...')).toBeInTheDocument()
    })

    it('should render with custom context', () => {
      render(<LoadingSpinner context="user data" />)
      
      expect(screen.getByLabelText('Loading user data')).toBeInTheDocument()
      expect(screen.getByText('Loading user data, please wait...')).toBeInTheDocument()
    })

    it('should apply size classes correctly', () => {
      const { rerender } = render(<LoadingSpinner size="sm" />)
      
      let spinner = screen.getByRole('status').firstChild
      expect(spinner).toHaveClass('h-4', 'w-4')
      
      rerender(<LoadingSpinner size="lg" />)
      spinner = screen.getByRole('status').firstChild
      expect(spinner).toHaveClass('h-8', 'w-8')
    })

    it('should announce loading state when context is provided', () => {
      const { announceLoadingState } = require('@/lib/accessibility')
      
      render(<LoadingSpinner context="test data" />)
      
      expect(announceLoadingState).toHaveBeenCalledWith(true, 'test data')
    })

    it('should have proper accessibility attributes', () => {
      render(<LoadingSpinner context="test" />)
      
      const container = screen.getByRole('status')
      expect(container).toHaveAttribute('aria-label', 'Loading test')
      expect(container).toHaveAttribute('aria-live', 'polite')
      
      // Spinner should be hidden from screen readers
      const spinner = container.firstChild
      expect(spinner).toHaveAttribute('aria-hidden', 'true')
    })
  })

  describe('LoadingOverlay', () => {
    const TestContent = () => <div data-testid="test-content">Test Content</div>

    it('should render children when not visible', () => {
      render(
        <LoadingOverlay isVisible={false} context="test">
          <TestContent />
        </LoadingOverlay>
      )
      
      expect(screen.getByTestId('test-content')).toBeInTheDocument()
      expect(screen.queryByRole('status')).not.toBeInTheDocument()
    })

    it('should render overlay when visible', () => {
      render(
        <LoadingOverlay isVisible={true} context="saving">
          <TestContent />
        </LoadingOverlay>
      )
      
      expect(screen.getByTestId('test-content')).toBeInTheDocument()
      expect(screen.getByRole('status')).toBeInTheDocument()
      expect(screen.getByLabelText('Loading saving')).toBeInTheDocument()
    })

    it('should make content non-interactive when overlay is visible', () => {
      render(
        <LoadingOverlay isVisible={true} context="test">
          <button>Click me</button>
        </LoadingOverlay>
      )
      
      const button = screen.getByRole('button')
      expect(button.closest('div')).toHaveClass('opacity-50', 'pointer-events-none')
    })

    it('should announce loading state changes', () => {
      const { announceLoadingState } = require('@/lib/accessibility')
      
      const { rerender } = render(
        <LoadingOverlay isVisible={false} context="test">
          <TestContent />
        </LoadingOverlay>
      )
      
      rerender(
        <LoadingOverlay isVisible={true} context="test">
          <TestContent />
        </LoadingOverlay>
      )
      
      expect(announceLoadingState).toHaveBeenCalledWith(true, 'test')
      
      rerender(
        <LoadingOverlay isVisible={false} context="test">
          <TestContent />
        </LoadingOverlay>
      )
      
      expect(announceLoadingState).toHaveBeenCalledWith(false, 'test')
    })

    it('should have proper overlay styling', () => {
      render(
        <LoadingOverlay isVisible={true} context="test">
          <TestContent />
        </LoadingOverlay>
      )
      
      const overlay = screen.getByRole('status')
      expect(overlay).toHaveClass('absolute', 'inset-0')
      expect(overlay.parentElement).toHaveClass('relative')
    })
  })

  describe('accessibility compliance', () => {
    it('should have consistent role attributes across components', () => {
      render(
        <div>
          <TableSkeleton context="test1" />
          <FormSkeleton context="test2" />
          <CardSkeleton context="test3" />
          <LoadingSpinner context="test4" />
        </div>
      )
      
      const statusElements = screen.getAllByRole('status')
      expect(statusElements).toHaveLength(4)
      
      statusElements.forEach(element => {
        expect(element).toHaveAttribute('aria-live', 'polite')
      })
    })

    it('should provide screen reader text for all loading states', () => {
      render(
        <div>
          <TableSkeleton context="table" />
          <FormSkeleton context="form" />
          <CardSkeleton context="card" />
          <LoadingSpinner context="spinner" />
        </div>
      )
      
      expect(screen.getByText('Loading table data, please wait...')).toBeInTheDocument()
      expect(screen.getByText('Loading form form, please wait...')).toBeInTheDocument()
      expect(screen.getByText('Loading card, please wait...')).toBeInTheDocument()
      expect(screen.getByText('Loading spinner, please wait...')).toBeInTheDocument()
    })
  })
})
