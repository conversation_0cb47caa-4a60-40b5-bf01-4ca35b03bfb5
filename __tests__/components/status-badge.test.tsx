/**
 * @jest-environment jsdom
 */

import { render, screen } from '@testing-library/react'
import { StatusBadge } from '@/components/status-badge'
import type { AppraisalStatus } from '@/lib/types'

// Mock the accessibility module
jest.mock('@/lib/accessibility', () => ({
  getStatusAnnouncement: jest.fn((status) => `Status announcement for ${status}`),
}))

describe('StatusBadge', () => {
  const testCases: { status: AppraisalStatus; expectedLabel: string; expectedClass: string }[] = [
    {
      status: 'submitted',
      expectedLabel: 'Submitted',
      expectedClass: 'bg-green-100 text-green-800',
    },
    {
      status: 'draft',
      expectedLabel: 'Draft',
      expectedClass: 'bg-yellow-100 text-yellow-800',
    },
    {
      status: 'not-started',
      expectedLabel: 'Not Started',
      expectedClass: 'bg-gray-100 text-gray-800',
    },
  ]

  testCases.forEach(({ status, expectedLabel, expectedClass }) => {
    describe(`when status is ${status}`, () => {
      it('should render the correct label', () => {
        render(<StatusBadge status={status} />)
        
        expect(screen.getByText(expectedLabel)).toBeInTheDocument()
      })

      it('should have correct styling classes', () => {
        render(<StatusBadge status={status} />)
        
        const badge = screen.getByText(expectedLabel)
        expect(badge).toHaveClass('font-medium')
        
        // Check for some of the color classes (exact class checking can be brittle)
        const classList = badge.className
        if (status === 'submitted') {
          expect(classList).toContain('green')
        } else if (status === 'draft') {
          expect(classList).toContain('yellow')
        } else if (status === 'not-started') {
          expect(classList).toContain('gray')
        }
      })

      it('should have proper accessibility attributes', () => {
        render(<StatusBadge status={status} />)
        
        const badge = screen.getByText(expectedLabel)
        expect(badge).toHaveAttribute('aria-label', `Status announcement for ${status}`)
      })

      it('should have a descriptive title attribute', () => {
        render(<StatusBadge status={status} />)
        
        const badge = screen.getByText(expectedLabel)
        expect(badge).toHaveAttribute('title')
        
        const title = badge.getAttribute('title')
        expect(title).toBeTruthy()
        expect(title).toContain('Appraisal')
      })
    })
  })

  describe('accessibility features', () => {
    it('should call getStatusAnnouncement with the correct status', () => {
      const { getStatusAnnouncement } = require('@/lib/accessibility')
      
      render(<StatusBadge status="submitted" />)
      
      expect(getStatusAnnouncement).toHaveBeenCalledWith('submitted')
    })

    it('should be focusable when used in interactive contexts', () => {
      render(
        <button>
          <StatusBadge status="draft" />
        </button>
      )
      
      const button = screen.getByRole('button')
      expect(button).toBeInTheDocument()
      
      button.focus()
      expect(button).toHaveFocus()
    })

    it('should work with screen readers', () => {
      render(<StatusBadge status="submitted" />)
      
      const badge = screen.getByText('Submitted')
      
      // Should have aria-label for screen readers
      expect(badge).toHaveAttribute('aria-label')
      
      // Should have title for additional context
      expect(badge).toHaveAttribute('title')
    })
  })

  describe('visual appearance', () => {
    it('should render as a badge element', () => {
      render(<StatusBadge status="submitted" />)
      
      const badge = screen.getByText('Submitted')
      expect(badge).toBeInTheDocument()
      
      // Should have badge-like styling
      expect(badge).toHaveClass('font-medium')
    })

    it('should have consistent styling across all statuses', () => {
      const { rerender } = render(<StatusBadge status="submitted" />)
      
      let badge = screen.getByText('Submitted')
      expect(badge).toHaveClass('font-medium')
      
      rerender(<StatusBadge status="draft" />)
      badge = screen.getByText('Draft')
      expect(badge).toHaveClass('font-medium')
      
      rerender(<StatusBadge status="not-started" />)
      badge = screen.getByText('Not Started')
      expect(badge).toHaveClass('font-medium')
    })
  })

  describe('integration with design system', () => {
    it('should use the Badge component from UI library', () => {
      render(<StatusBadge status="submitted" />)
      
      const badge = screen.getByText('Submitted')
      
      // Should have the outline variant class or similar badge styling
      const classList = badge.className
      expect(classList).toContain('font-medium')
    })

    it('should support dark mode classes', () => {
      render(<StatusBadge status="submitted" />)
      
      const badge = screen.getByText('Submitted')
      const classList = badge.className
      
      // Should include dark mode classes
      expect(classList).toMatch(/dark:/)
    })
  })

  describe('error handling', () => {
    it('should handle undefined status gracefully', () => {
      // TypeScript would prevent this, but test runtime behavior
      expect(() => {
        render(<StatusBadge status={undefined as any} />)
      }).not.toThrow()
    })

    it('should handle empty string status', () => {
      expect(() => {
        render(<StatusBadge status={'' as any} />)
      }).not.toThrow()
    })
  })
})
