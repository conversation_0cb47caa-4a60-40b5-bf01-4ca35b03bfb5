/**
 * @jest-environment jsdom
 */

import {
  announceToScreenReader,
  getStatusAnnouncement,
  getTableAnnouncement,
  getSortAnnouncement,
  generateId,
  announceFormError,
  announceFormSuccess,
  announceLoadingState,
  announceError,
} from '@/lib/accessibility'

// Mock the actual implementation since we're testing the interface
jest.unmock('@/lib/accessibility')

describe('Accessibility Utilities', () => {
  beforeEach(() => {
    document.body.innerHTML = ''
    jest.clearAllMocks()
  })

  describe('announceToScreenReader', () => {
    it('should create an announcement element with correct attributes', () => {
      announceToScreenReader('Test message', 'polite')
      
      const announcements = document.querySelectorAll('[aria-live="polite"]')
      expect(announcements).toHaveLength(1)
      
      const announcement = announcements[0]
      expect(announcement).toHaveAttribute('aria-atomic', 'true')
      expect(announcement).toHaveClass('sr-only')
      expect(announcement.textContent).toBe('Test message')
    })

    it('should default to polite priority', () => {
      announceToScreenReader('Test message')
      
      const announcement = document.querySelector('[aria-live="polite"]')
      expect(announcement).toBeInTheDocument()
    })

    it('should support assertive priority', () => {
      announceToScreenReader('Urgent message', 'assertive')
      
      const announcement = document.querySelector('[aria-live="assertive"]')
      expect(announcement).toBeInTheDocument()
      expect(announcement?.textContent).toBe('Urgent message')
    })

    it('should remove announcement after timeout', (done) => {
      announceToScreenReader('Temporary message')

      const announcement = document.querySelector('[aria-live="polite"]')
      expect(announcement).toBeInTheDocument()

      setTimeout(() => {
        // Check if the announcement was removed
        const currentAnnouncement = document.querySelector('[aria-live="polite"]')
        expect(currentAnnouncement).not.toBeInTheDocument()
        done()
      }, 1100) // Slightly longer than the 1000ms timeout
    })
  })

  describe('getStatusAnnouncement', () => {
    it('should return correct announcements for known statuses', () => {
      expect(getStatusAnnouncement('submitted')).toBe('Submitted for review')
      expect(getStatusAnnouncement('draft')).toBe('Saved as draft')
      expect(getStatusAnnouncement('missing')).toBe('Not yet started')
      expect(getStatusAnnouncement('active')).toBe('Active employee')
      expect(getStatusAnnouncement('inactive')).toBe('Inactive employee')
    })

    it('should return the original status for unknown statuses', () => {
      expect(getStatusAnnouncement('unknown-status')).toBe('unknown-status')
    })

    it('should handle performance ratings', () => {
      expect(getStatusAnnouncement('below-expectations')).toBe('Performance below expectations')
      expect(getStatusAnnouncement('meets-expectations')).toBe('Performance meets expectations')
      expect(getStatusAnnouncement('exceeds-expectations')).toBe('Performance exceeds expectations')
    })
  })

  describe('getTableAnnouncement', () => {
    it('should return correct table description', () => {
      expect(getTableAnnouncement(5, 3)).toBe('Table with 5 rows and 3 columns')
      expect(getTableAnnouncement(0, 5)).toBe('Table with 0 rows and 5 columns')
      expect(getTableAnnouncement(100, 10)).toBe('Table with 100 rows and 10 columns')
    })
  })

  describe('getSortAnnouncement', () => {
    it('should return correct sort announcements', () => {
      expect(getSortAnnouncement('Name', 'asc')).toBe('Name column, sorted ascending')
      expect(getSortAnnouncement('Date', 'desc')).toBe('Date column, sorted descending')
      expect(getSortAnnouncement('Status', null)).toBe('Status column, not sorted')
    })
  })

  describe('generateId', () => {
    it('should generate unique IDs with prefix', () => {
      const id1 = generateId('test')
      const id2 = generateId('test')
      
      expect(id1).toMatch(/^test-/)
      expect(id2).toMatch(/^test-/)
      expect(id1).not.toBe(id2)
    })

    it('should use default prefix when none provided', () => {
      const id = generateId()
      expect(id).toMatch(/^a11y-/)
    })
  })

  describe('announceFormError', () => {
    it('should announce form errors with assertive priority', () => {
      announceFormError('Email', 'This field is required')
      
      // Should call announceToScreenReader with assertive priority
      const announcement = document.querySelector('[aria-live="assertive"]')
      expect(announcement).toBeInTheDocument()
    })
  })

  describe('announceFormSuccess', () => {
    it('should announce form success with polite priority', () => {
      announceFormSuccess('Form submitted successfully')
      
      const announcement = document.querySelector('[aria-live="polite"]')
      expect(announcement).toBeInTheDocument()
    })
  })

  describe('announceLoadingState', () => {
    it('should announce loading start and end', () => {
      announceLoadingState(true, 'user data')
      let announcement = document.querySelector('[aria-live="polite"]')
      expect(announcement?.textContent).toContain('Loading user data')
      
      // Clear previous announcement
      document.body.innerHTML = ''
      
      announceLoadingState(false, 'user data')
      announcement = document.querySelector('[aria-live="polite"]')
      expect(announcement?.textContent).toContain('user data loaded')
    })
  })

  describe('announceError', () => {
    it('should announce errors with context', () => {
      announceError('Network error', 'data loading')
      
      const announcement = document.querySelector('[aria-live="assertive"]')
      expect(announcement?.textContent).toContain('Error in data loading: Network error')
    })

    it('should announce errors without context', () => {
      announceError('Something went wrong')
      
      const announcement = document.querySelector('[aria-live="assertive"]')
      expect(announcement?.textContent).toContain('Error: Something went wrong')
    })
  })

  describe('Focus Management', () => {
    it('should handle focus trap setup', () => {
      // Create a container with focusable elements
      const container = document.createElement('div')
      container.innerHTML = `
        <button>First</button>
        <input type="text" />
        <button>Last</button>
      `
      document.body.appendChild(container)

      // Test that focus trap can be set up without errors
      expect(() => {
        // This would normally call trapFocus from the accessibility module
        const focusableElements = container.querySelectorAll(
          'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        )
        expect(focusableElements).toHaveLength(3)
      }).not.toThrow()
    })
  })

  describe('Keyboard Navigation', () => {
    it('should handle arrow key navigation', () => {
      const items = [
        document.createElement('button'),
        document.createElement('button'),
        document.createElement('button'),
      ]
      
      items.forEach(item => document.body.appendChild(item))
      
      let currentIndex = 0
      const onIndexChange = jest.fn((index) => {
        currentIndex = index
      })

      // Simulate arrow down
      const event = new KeyboardEvent('keydown', { key: 'ArrowDown' })
      
      // Test the navigation logic
      expect(() => {
        if (event.key === 'ArrowDown') {
          const nextIndex = currentIndex < items.length - 1 ? currentIndex + 1 : 0
          onIndexChange(nextIndex)
        }
      }).not.toThrow()
      
      expect(onIndexChange).toHaveBeenCalledWith(1)
    })
  })

  describe('Skip Links', () => {
    it('should create skip links with correct attributes', () => {
      const skipLink = document.createElement('a')
      skipLink.href = '#main-content'
      skipLink.textContent = 'Skip to main content'
      skipLink.className = 'sr-only focus:not-sr-only'
      
      expect(skipLink.href).toContain('#main-content')
      expect(skipLink.textContent).toBe('Skip to main content')
      expect(skipLink).toHaveClass('sr-only')
    })
  })
})
