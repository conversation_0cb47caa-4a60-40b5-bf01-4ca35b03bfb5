"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { UserPlus, Building, Users } from "lucide-react"
import { toast } from "sonner"

export default function AddPeoplePage() {
  const [activeTab, setActiveTab] = useState<"employee" | "manager" | "department">("employee")

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    const formData = new FormData(e.currentTarget)
    
    // Mock submission - replace with actual API call
    console.log("Form submitted:", Object.fromEntries(formData))
    toast.success(`${activeTab} added successfully!`)
    
    // Reset form
    e.currentTarget.reset()
  }

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="mb-6">
        <h1 className="text-2xl font-semibold mb-2">Add People & Departments</h1>
        <p className="text-muted-foreground">
          Add new employees, managers, or departments to the system
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="flex mb-6 border-b">
        <button
          onClick={() => setActiveTab("employee")}
          className={`px-4 py-2 font-medium border-b-2 transition-colors ${
            activeTab === "employee"
              ? "border-primary text-primary"
              : "border-transparent text-muted-foreground hover:text-foreground"
          }`}
        >
          <Users className="w-4 h-4 inline mr-2" />
          Employee
        </button>
        <button
          onClick={() => setActiveTab("manager")}
          className={`px-4 py-2 font-medium border-b-2 transition-colors ${
            activeTab === "manager"
              ? "border-primary text-primary"
              : "border-transparent text-muted-foreground hover:text-foreground"
          }`}
        >
          <UserPlus className="w-4 h-4 inline mr-2" />
          Manager
        </button>
        <button
          onClick={() => setActiveTab("department")}
          className={`px-4 py-2 font-medium border-b-2 transition-colors ${
            activeTab === "department"
              ? "border-primary text-primary"
              : "border-transparent text-muted-foreground hover:text-foreground"
          }`}
        >
          <Building className="w-4 h-4 inline mr-2" />
          Department
        </button>
      </div>

      {/* Employee Form */}
      {activeTab === "employee" && (
        <Card>
          <CardHeader>
            <CardTitle>Add New Employee</CardTitle>
            <CardDescription>
              Enter employee details including compensation information
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="fullName">Full Name</Label>
                  <Input
                    id="fullName"
                    name="fullName"
                    placeholder="John Doe"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="department">Department</Label>
                  <Select name="departmentId" required>
                    <SelectTrigger>
                      <SelectValue placeholder="Select department" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">Engineering</SelectItem>
                      <SelectItem value="2">Sales</SelectItem>
                      <SelectItem value="3">Marketing</SelectItem>
                      <SelectItem value="4">HR</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="compensation">Compensation Type</Label>
                  <Select name="compensation" required>
                    <SelectTrigger>
                      <SelectValue placeholder="Select compensation type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="hourly">Hourly</SelectItem>
                      <SelectItem value="monthly">Monthly</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="rate">Rate</Label>
                  <Input
                    id="rate"
                    name="rate"
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    required
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="manager">Manager (Optional)</Label>
                <Select name="managerId">
                  <SelectTrigger>
                    <SelectValue placeholder="Select manager" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="you">CJN Automation</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Button type="submit" className="w-full">
                <Users className="w-4 h-4 mr-2" />
                Add Employee
              </Button>
            </form>
          </CardContent>
        </Card>
      )}

      {/* Manager Form */}
      {activeTab === "manager" && (
        <Card>
          <CardHeader>
            <CardTitle>Add New Manager</CardTitle>
            <CardDescription>
              Add a new manager with their role and permissions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="managerName">Full Name</Label>
                  <Input
                    id="managerName"
                    name="fullName"
                    placeholder="Jane Smith"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="role">Role</Label>
                <Select name="role" required>
                  <SelectTrigger>
                    <SelectValue placeholder="Select role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="manager">Manager</SelectItem>
                    <SelectItem value="hr-admin">HR Admin</SelectItem>
                    <SelectItem value="accountant">Accountant</SelectItem>
                    <SelectItem value="super-admin">Super Admin</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Button type="submit" className="w-full">
                <UserPlus className="w-4 h-4 mr-2" />
                Add Manager
              </Button>
            </form>
          </CardContent>
        </Card>
      )}

      {/* Department Form */}
      {activeTab === "department" && (
        <Card>
          <CardHeader>
            <CardTitle>Add New Department</CardTitle>
            <CardDescription>
              Create a new department for organizing employees
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <Label htmlFor="departmentName">Department Name</Label>
                <Input
                  id="departmentName"
                  name="name"
                  placeholder="Customer Success"
                  required
                />
              </div>

              <Button type="submit" className="w-full">
                <Building className="w-4 h-4 mr-2" />
                Add Department
              </Button>
            </form>
          </CardContent>
        </Card>
      )}
    </div>
  )
}