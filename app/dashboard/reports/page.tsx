import { getAccountingDataForUser } from "@/lib/data/index"
import { AccountingTable } from "@/components/accounting-table"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { BarChart3, FileText, TrendingUp, Users } from "lucide-react"
import { requirePermission, hasPermission } from "@/lib/auth"

export default async function ReportsPage() {
  // Require approval:read permission to access this page
  const user = await requirePermission('approval:read')
  
  const accountingData = await getAccountingDataForUser()
  
  // Check permissions for different sections
  const canViewPerformance = hasPermission(user.role, 'appraisal:read')
  const canViewDepartments = hasPermission(user.role, 'department:read')
  const canExportData = hasPermission(user.role, 'approval:export')
  
  // Determine available tabs based on permissions
  const availableTabs = []
  
  // All users with approval:read can see approvals
  availableTabs.push('approvals')
  
  // Only users with appraisal:read can see performance analytics
  if (canViewPerformance) {
    availableTabs.push('performance')
    availableTabs.push('trends')
  }
  
  // Only users with department:read can see department analytics
  if (canViewDepartments) {
    availableTabs.push('departments')
  }
  
  const defaultTab = availableTabs[0] || 'approvals'

  console.log('[REPORTS] Available tabs:', availableTabs)
  console.log('[REPORTS] Default tab:', defaultTab)
  console.log('[REPORTS] User permissions:', { canViewPerformance, canViewDepartments, canExportData })

  return (
    <div className="p-4 sm:p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold tracking-tight">Reports & Analytics</h1>
        <p className="text-muted-foreground">
          Comprehensive reporting and analytics dashboard for the July 2025 period.
        </p>
      </div>

      <Tabs defaultValue={defaultTab} className="space-y-6">
        <TabsList className={`grid w-full ${
          availableTabs.length === 1 ? 'grid-cols-1' :
          availableTabs.length === 2 ? 'grid-cols-2' :
          availableTabs.length === 3 ? 'grid-cols-3' :
          'grid-cols-4'
        }`}>
          {availableTabs.includes('approvals') && (
            <TabsTrigger value="approvals" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Approvals
            </TabsTrigger>
          )}
          {availableTabs.includes('performance') && (
            <TabsTrigger value="performance" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Performance
            </TabsTrigger>
          )}
          {availableTabs.includes('trends') && (
            <TabsTrigger value="trends" className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              Trends
            </TabsTrigger>
          )}
          {availableTabs.includes('departments') && (
            <TabsTrigger value="departments" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Departments
            </TabsTrigger>
          )}
        </TabsList>

        {availableTabs.includes('approvals') && (
          <TabsContent value="approvals" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Payroll Approvals</CardTitle>
                <CardDescription>
                  Status of all employee appraisals for the current period. 
                  {canExportData && ' Export submitted appraisals for payroll processing.'}
                  {user.role === 'manager' && ' Showing only employees under your supervision.'}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <AccountingTable data={accountingData} />
              </CardContent>
            </Card>
          </TabsContent>
        )}

        {availableTabs.includes('performance') && (
          <TabsContent value="performance" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Performance Overview</CardTitle>
                <CardDescription>
                  Performance metrics and distribution across {user.role === 'manager' ? 'your team' : 'all employees'}.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-12 text-muted-foreground">
                  Performance analytics will be available here.
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        )}

        {availableTabs.includes('trends') && (
          <TabsContent value="trends" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Performance Trends</CardTitle>
                <CardDescription>
                  Historical performance trends and patterns over time.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-12 text-muted-foreground">
                  Trend analysis will be available here.
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        )}

        {availableTabs.includes('departments') && (
          <TabsContent value="departments" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Department Analytics</CardTitle>
                <CardDescription>
                  Performance breakdown by department and team.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-12 text-muted-foreground">
                  Department analytics will be available here.
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        )}
      </Tabs>
    </div>
  )
}