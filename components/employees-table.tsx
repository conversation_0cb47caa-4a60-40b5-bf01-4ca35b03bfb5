"use client"

import * as React from "react"
import {
  type ColumnDef,
  type ColumnFiltersState,
  type SortingState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table"
import { MoreHorizontal, ArrowUpDown } from "lucide-react"

import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import type { Employee, Department, Manager } from "@/lib/types"
import { EmployeeFormDialog } from "./employee-form-dialog"
import {
  announceToScreenReader,
  getStatusAnnouncement,
  getTableAnnouncement,
  getSortAnnouncement
} from "@/lib/accessibility"

export function EmployeesTable({
  data,
  departments,
  managers,
  onRefresh
}: {
  data: Employee[]
  departments: Department[]
  managers: Manager[]
  onRefresh?: () => void
}) {
  const [sorting, setSorting] = React.useState<SortingState>([])
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
  const [isFormOpen, setIsFormOpen] = React.useState(false)
  const [selectedEmployee, setSelectedEmployee] = React.useState<Employee | null>(null)
  const [announceUpdate, setAnnounceUpdate] = React.useState<string>("")

  // Announce table updates to screen readers
  React.useEffect(() => {
    if (announceUpdate) {
      announceToScreenReader(announceUpdate)
      setAnnounceUpdate("")
    }
  }, [announceUpdate])

  // Announce table information on mount
  React.useEffect(() => {
    const tableAnnouncement = getTableAnnouncement(data.length, 6)
    announceToScreenReader(tableAnnouncement)
  }, [data.length])

  const handleEdit = (employee: Employee) => {
    console.log('[A11Y] Opening edit dialog for employee:', employee.fullName)
    setSelectedEmployee(employee)
    setIsFormOpen(true)
    setAnnounceUpdate(`Opening edit form for ${employee.fullName}`)
  }

  const handleAddNew = () => {
    console.log('[A11Y] Opening new employee dialog')
    setSelectedEmployee(null)
    setIsFormOpen(true)
    setAnnounceUpdate("Opening new employee form")
  }

  const handleFormClose = () => {
    console.log('[A11Y] Closing employee form dialog')
    setIsFormOpen(false)
    setAnnounceUpdate("Employee form closed")
    setTimeout(() => setSelectedEmployee(null), 300)
  }

  const columns: ColumnDef<Employee>[] = [
    {
      accessorKey: "fullName",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => {
            const newSorting = column.getIsSorted() === "asc" ? "desc" : "asc"
            column.toggleSorting(column.getIsSorted() === "asc")
            setAnnounceUpdate(getSortAnnouncement("Name", newSorting))
          }}
          className="h-auto p-0 font-medium hover:bg-transparent"
          aria-label={getSortAnnouncement("Name", column.getIsSorted() || null)}
        >
          Name
          <ArrowUpDown className="ml-2 h-4 w-4" aria-hidden="true" />
        </Button>
      ),
    },
    {
      accessorKey: "departmentName",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => {
            const newSorting = column.getIsSorted() === "asc" ? "desc" : "asc"
            column.toggleSorting(column.getIsSorted() === "asc")
            setAnnounceUpdate(getSortAnnouncement("Department", newSorting))
          }}
          className="h-auto p-0 font-medium hover:bg-transparent"
          aria-label={getSortAnnouncement("Department", column.getIsSorted() || null)}
        >
          Department
          <ArrowUpDown className="ml-2 h-4 w-4" aria-hidden="true" />
        </Button>
      ),
    },
    {
      accessorKey: "managerName",
      header: "Manager",
    },
    {
      accessorKey: "compensation",
      header: "Compensation",
      cell: ({ row }) => <div className="capitalize">{row.getValue("compensation") as string}</div>,
    },
    {
      accessorKey: "rate",
      header: () => <div className="text-right">Rate</div>,
      cell: ({ row }) => {
        const rate = Number.parseFloat(row.getValue("rate"))
        const isHourly = row.getValue("compensation") === "hourly"
        const formatted = new Intl.NumberFormat("en-US", { style: "currency", currency: "USD" }).format(rate)
        return (
          <div className="text-right font-medium">
            {formatted} {isHourly ? "/ hr" : ""}
          </div>
        )
      },
    },
    {
      accessorKey: "active",
      header: "Status",
      cell: ({ row }) => {
        const isActive = row.getValue("active")
        const statusText = isActive ? "Active" : "Inactive"
        return (
          <Badge
            variant={isActive ? "default" : "outline"}
            className={isActive ? "bg-green-600" : ""}
            aria-label={getStatusAnnouncement(statusText.toLowerCase())}
          >
            {statusText}
          </Badge>
        )
      },
    },
    {
      id: "actions",
      header: () => <span className="sr-only">Actions</span>,
      cell: ({ row }) => {
        const employee = row.original
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="h-8 w-8 p-0"
                aria-label={`Actions for ${employee.fullName}`}
              >
                <span className="sr-only">Open menu for {employee.fullName}</span>
                <MoreHorizontal className="h-4 w-4" aria-hidden="true" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions for {employee.fullName}</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => handleEdit(employee)}>
                Edit Employee
              </DropdownMenuItem>
              <DropdownMenuItem>
                {employee.active ? "Deactivate" : "Activate"} Employee
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )
      },
    },
  ]

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    state: { sorting, columnFilters },
  })

  return (
    <div>
      <div className="flex items-center py-4">
        <Input
          placeholder="Filter by name..."
          value={(table.getColumn("fullName")?.getFilterValue() as string) ?? ""}
          onChange={(event) => {
            const value = event.target.value
            table.getColumn("fullName")?.setFilterValue(value)
            if (value) {
              setAnnounceUpdate(`Filtering employees by name: ${value}`)
            } else {
              setAnnounceUpdate("Filter cleared, showing all employees")
            }
          }}
          className="max-w-sm"
          aria-label="Filter employees by name"
        />
        <Button onClick={handleAddNew} className="ml-auto">
          Add New Employee
        </Button>
      </div>
      <div className="rounded-md border">
        <Table
          aria-label={getTableAnnouncement(data.length, 6)}
          role="table"
        >
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id} role="row">
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id} role="columnheader">
                    {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row, index) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                  role="row"
                  aria-rowindex={index + 2} // +2 because header is row 1
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell
                      key={cell.id}
                      role="gridcell"
                    >
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow role="row">
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                  role="gridcell"
                  aria-label="No employees found"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <nav className="flex items-center justify-end space-x-2 py-4" aria-label="Table pagination">
        <Button
          variant="outline"
          size="sm"
          onClick={() => {
            table.previousPage()
            setAnnounceUpdate("Moved to previous page")
          }}
          disabled={!table.getCanPreviousPage()}
          aria-label="Go to previous page"
        >
          Previous
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => {
            table.nextPage()
            setAnnounceUpdate("Moved to next page")
          }}
          disabled={!table.getCanNextPage()}
          aria-label="Go to next page"
        >
          Next
        </Button>
      </nav>
      <EmployeeFormDialog
        isOpen={isFormOpen}
        onClose={handleFormClose}
        employee={selectedEmployee}
        departments={departments}
        managers={managers}
      />
    </div>
  )
}
