"use client"

import { useState } from "react"
import React from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { CalendarDays, Clock, AlertCircle, Check } from "lucide-react"
import { toast } from "sonner"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

import { submitPTORequestAction } from "@/lib/actions"
import { ptoRequestFormSchema } from "@/lib/schemas"
import type { PTORequestType, PTOBalance } from "@/lib/types"

interface PTORequestFormProps {
  balance: PTOBalance
  onRequestSubmitted?: () => void
  trigger?: React.ReactNode
}

type FormData = {
  requestType: PTORequestType
  startDate: string
  endDate: string
  daysRequested: number
  reason?: string
}

const requestTypeOptions = [
  { value: 'vacation', label: 'Vacation', description: 'Personal time off for rest and relaxation' },
  { value: 'sick', label: 'Sick Leave', description: 'Time off due to illness or medical appointments' },
  { value: 'personal', label: 'Personal Day', description: 'Time off for personal matters' },
  { value: 'emergency', label: 'Emergency', description: 'Urgent unexpected circumstances' },
] as const

export function PTORequestForm({ balance, onRequestSubmitted, trigger }: PTORequestFormProps) {
  const [open, setOpen] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const form = useForm<FormData>({
    resolver: zodResolver(ptoRequestFormSchema),
    defaultValues: {
      requestType: 'vacation',
      startDate: '',
      endDate: '',
      daysRequested: 1,
      reason: '',
    },
  })

  // Calculate days requested based on date range
  const calculateDays = (startDate: string, endDate: string) => {
    if (!startDate || !endDate) return 0
    
    const start = new Date(startDate)
    const end = new Date(endDate)
    const diffTime = Math.abs(end.getTime() - start.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1
    
    return diffDays
  }

  // Watch for date changes to auto-calculate days
  const startDate = form.watch('startDate')
  const endDate = form.watch('endDate')
  const daysRequested = form.watch('daysRequested')

  // Update days requested when dates change
  React.useEffect(() => {
    if (startDate && endDate) {
      const calculatedDays = calculateDays(startDate, endDate)
      if (calculatedDays !== daysRequested) {
        form.setValue('daysRequested', calculatedDays)
      }
    }
  }, [startDate, endDate, daysRequested, form])

  const onSubmit = async (data: FormData) => {
    setIsSubmitting(true)
    
    try {
      // Check if user has sufficient balance
      if (data.daysRequested > balance.availableDays) {
        toast.error(`Insufficient PTO balance. You have ${balance.availableDays} days available.`)
        return
      }

      // Create FormData for server action
      const formData = new FormData()
      formData.append('requestType', data.requestType)
      formData.append('startDate', data.startDate)
      formData.append('endDate', data.endDate)
      formData.append('daysRequested', data.daysRequested.toString())
      if (data.reason) {
        formData.append('reason', data.reason)
      }

      const result = await submitPTORequestAction(formData)

      if (result.success) {
        toast.success('message' in result ? result.message : 'PTO request submitted successfully')
        form.reset()
        setOpen(false)
        onRequestSubmitted?.()
      } else {
        toast.error('error' in result ? result.error : 'Failed to submit PTO request')
      }
    } catch (error) {
      // console.error('Error submitting PTO request:', error)
      toast.error('An unexpected error occurred. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const defaultTrigger = (
    <Button>
      <CalendarDays className="mr-2 h-4 w-4" />
      Request Time Off
    </Button>
  )

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Request Time Off</DialogTitle>
          <DialogDescription>
            Submit a PTO request for manager approval. You have {balance.availableDays} days available.
          </DialogDescription>
        </DialogHeader>

        {/* Balance Summary */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Current Balance</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-sm font-medium text-blue-600">{balance.totalDays}</div>
                <div className="text-xs text-muted-foreground">Total</div>
              </div>
              <div>
                <div className="text-sm font-medium text-orange-600">{balance.usedDays}</div>
                <div className="text-xs text-muted-foreground">Used</div>
              </div>
              <div>
                <div className="text-sm font-medium text-green-600">{balance.availableDays}</div>
                <div className="text-xs text-muted-foreground">Available</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {/* Request Type */}
            <FormField
              control={form.control}
              name="requestType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Request Type</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select request type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {requestTypeOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          <div className="flex flex-col">
                            <span>{option.label}</span>
                            <span className="text-xs text-muted-foreground">
                              {option.description}
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Date Range */}
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="startDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Start Date</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="endDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>End Date</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Days Requested */}
            <FormField
              control={form.control}
              name="daysRequested"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Days Requested</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min="1"
                      max="7"
                      {...field}
                      onChange={(e) => field.onChange(Number(e.target.value))}
                    />
                  </FormControl>
                  <FormDescription>
                    Automatically calculated based on date range
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Balance Check Alert */}
            {daysRequested > balance.availableDays && (
              <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg">
                <AlertCircle className="h-4 w-4 text-red-500" />
                <div className="text-sm text-red-700">
                  <strong>Insufficient Balance:</strong> You're requesting {daysRequested} days but only have {balance.availableDays} available.
                </div>
              </div>
            )}

            {/* Reason */}
            <FormField
              control={form.control}
              name="reason"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Reason (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Brief description of the reason for your time off request..."
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Optional details about your time off request
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Submit Button */}
            <div className="flex justify-end space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => setOpen(false)}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting || daysRequested > balance.availableDays}
              >
                {isSubmitting ? (
                  <>
                    <Clock className="mr-2 h-4 w-4 animate-spin" />
                    Submitting...
                  </>
                ) : (
                  <>
                    <Check className="mr-2 h-4 w-4" />
                    Submit Request
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}