-- This script provides sample data for development.
-- In a real application, this data would be managed by the HR Admin.

-- Create Departments
INSERT INTO departments (name) VALUES ('Engineering'), ('Product'), ('Design'), ('Marketing');

-- Create a Manager (replace with a real Clerk user_id)
INSERT INTO managers (user_id, full_name) VALUES ('a1b2c3d4-e5f6-7890-1234-567890abcdef', 'Francesco');

-- Assign a role to the manager
INSERT INTO user_roles (user_id, role) VALUES ('a1b2c3d4-e5f6-7890-1234-567890abcdef', 'manager');

-- Create Employees
DO $$
DECLARE
    eng_dept_id uuid;
    prod_dept_id uuid;
    manager_user_id uuid := 'a1b2c3d4-e5f6-7890-1234-567890abcdef';
BEGIN
    SELECT id INTO eng_dept_id FROM departments WHERE name = 'Engineering';
    SELECT id INTO prod_dept_id FROM departments WHERE name = 'Product';

    -- Engineering Team
    WITH new_employees AS (
        INSERT INTO employees (full_name, compensation, rate, department_id) VALUES
        ('Issa', 'monthly', 10000.00, eng_dept_id),
        ('Alice', 'monthly', 9500.00, eng_dept_id),
        ('Charlie', 'hourly', 75.00, eng_dept_id)
        RETURNING id
    )
    INSERT INTO employee_assignments (employee_id, manager_id)
    SELECT id, manager_user_id FROM new_employees;

    -- Product Team
    WITH new_employees AS (
        INSERT INTO employees (full_name, compensation, rate, department_id) VALUES
        ('David', 'monthly', 11000.00, prod_dept_id),
        ('Eve', 'hourly', 80.00, prod_dept_id)
        RETURNING id
    )
    INSERT INTO employee_assignments (employee_id, manager_id)
    SELECT id, manager_user_id FROM new_employees;
END $$;


-- Create an active appraisal period
INSERT INTO appraisal_periods (period_start, period_end)
VALUES (date_trunc('month', current_date), date_trunc('month', current_date) + interval '1 month - 1 day');

-- Create some sample appraisals
DO $$
DECLARE
    current_period_id uuid;
    manager_user_id uuid := 'a1b2c3d4-e5f6-7890-1234-567890abcdef';
    issa_employee_id uuid;
    alice_employee_id uuid;
BEGIN
    SELECT id INTO current_period_id FROM appraisal_periods WHERE closed = false ORDER BY period_start DESC LIMIT 1;
    SELECT id INTO issa_employee_id FROM employees WHERE full_name = 'Issa';
    SELECT id INTO alice_employee_id FROM employees WHERE full_name = 'Alice';
    
    -- A submitted appraisal for Issa
    INSERT INTO appraisals (period_id, employee_id, manager_id, q1, q2, q3, q4, q5, status, submitted_at) VALUES
    (current_period_id, issa_employee_id, manager_user_id, 'Excellent', 'Yes', 'Project Phoenix', 'Great teamwork', 'Promotion path', 'submitted', now() - interval '2 days');

    -- A draft appraisal for Alice
    INSERT INTO appraisals (period_id, employee_id, manager_id, q1, q2, status) VALUES
    (current_period_id, alice_employee_id, manager_user_id, 'Good', 'No', 'draft');
END $$;
