-- Admin Users Seed Data
-- This script creates the admin users with their roles and Clerk user IDs

-- Note: These user IDs should be replaced with actual Clerk user IDs 
-- when the users are created in Clerk dashboard

-- Create Bob (Super Admin)
INSERT INTO managers (user_id, full_name) VALUES 
('bob_clerk_user_id_placeholder', '<PERSON>');

INSERT INTO user_roles (user_id, role) VALUES 
('bob_clerk_user_id_placeholder', 'super-admin');

-- Create Tarek (Admin)
INSERT INTO managers (user_id, full_name) VALUES 
('tarek_clerk_user_id_placeholder', 'Tarek H');

INSERT INTO user_roles (user_id, role) VALUES 
('tarek_clerk_user_id_placeholder', 'admin');

-- Create Romy (Admin)
INSERT INTO managers (user_id, full_name) VALUES 
('romy_clerk_user_id_placeholder', 'Romy H');

INSERT INTO user_roles (user_id, role) VALUES 
('romy_clerk_user_id_placeholder', 'admin');

-- Create admin-specific table for admin management
CREATE TABLE admin_profiles (
  user_id         uuid primary key references managers(user_id),
  supervisor_id   uuid references managers(user_id), -- NULL for super-admin
  email           text not null unique,
  permissions     jsonb default '{}', -- Additional permissions config
  created_at      timestamptz default now(),
  updated_at      timestamptz default now()
);

-- Insert admin profile records
INSERT INTO admin_profiles (user_id, supervisor_id, email) VALUES 
('bob_clerk_user_id_placeholder', NULL, '<EMAIL>'),
('tarek_clerk_user_id_placeholder', 'bob_clerk_user_id_placeholder', '<EMAIL>'),
('romy_clerk_user_id_placeholder', 'bob_clerk_user_id_placeholder', '<EMAIL>');

-- Create function to update admin profile timestamp
CREATE OR REPLACE FUNCTION update_admin_profile_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for admin profile updates
CREATE TRIGGER update_admin_profiles_timestamp
  BEFORE UPDATE ON admin_profiles
  FOR EACH ROW
  EXECUTE FUNCTION update_admin_profile_timestamp();