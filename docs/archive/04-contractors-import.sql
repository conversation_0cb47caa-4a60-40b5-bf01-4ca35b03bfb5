-- Import Contractors from CSV Data
-- This script adds all contractors from the CSV files, excluding <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> who are now admins
-- 
-- PREREQUISITES: Run 01-schema.sql first to create the database structure
-- 
-- First, add 'monthly' to the compensation_type enum if it doesn't exist
ALTER TYPE appy_compensation_type ADD VALUE IF NOT EXISTS 'monthly';
COMMIT;

-- SUMMARY:
-- - Creates 7 departments: Operations, Marketing, Sales, Media, Finance, Legal, Executive (replaces 02-seed.sql departments)
-- - Adds 19 managers who supervise contractors (includes <PERSON>, <PERSON><PERSON>, <PERSON><PERSON> as admins)
-- - Imports 206+ contractors organized by actual departments (not job roles)
-- - Excludes <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> (now admins)
-- - Sets up proper manager-employee relationships
-- - Creates an active appraisal period (from 02-seed.sql)



-- First, let's create the actual departments we need
-- This replaces the sample departments from 02-seed.sql
INSERT INTO appy_departments (name) VALUES 
('Operations'),
('Marketing'),
('Sales'),
('Media'),
('Finance'),
('Legal'),
('Executive')
ON CONFLICT (name) DO NOTHING;

-- Get department IDs for reference
-- We'll need to replace these with actual UUIDs from the database

-- Create managers that don't exist yet (these are people who manage contractors)
INSERT INTO appy_managers (user_id, full_name, email) VALUES 
('mona_manager_id', 'Mona Bourgess', '<EMAIL>'),
('joey_manager_id', 'Joey Hourany', '<EMAIL>'),
('natalie_manager_id', 'Natalie Saginashvili', '<EMAIL>'),
('ned_manager_id', 'Ned Nedyalkov', '<EMAIL>'),
('mia_manager_id', 'Mia Owaini', '<EMAIL>'),
('gian_manager_id', 'Gian Carlo U. Calixto', '<EMAIL>'),
('marian_manager_id', 'Mariangelica Angulo', '<EMAIL>'),
('jessica_manager_id', 'Jessica Hasson', '<EMAIL>'),
('joseph_manager_id', 'Joseph Nemer', '<EMAIL>'),
('mario_manager_id', 'Mario Nawfal', '<EMAIL>'), -- CEO
-- Add the new admins as managers
('bob_clerk_user_id_placeholder', 'Bob Wazneh', '<EMAIL>'),
('tarek_clerk_user_id_placeholder', 'Tarek Hassoun', '<EMAIL>'),
('romy_clerk_user_id_placeholder', 'Romy Hemadeh', '<EMAIL>')
ON CONFLICT (user_id) DO NOTHING;

-- Set their roles as managers
INSERT INTO appy_user_roles (user_id, role) VALUES 
('mona_manager_id', 'manager'),
('joey_manager_id', 'manager'),
('natalie_manager_id', 'manager'),
('ned_manager_id', 'manager'),
('mia_manager_id', 'manager'),
('gian_manager_id', 'manager'),
('marian_manager_id', 'manager'),
('joseph_manager_id', 'manager'),
('mario_manager_id', 'super-admin'),
-- Add admin roles for Bob, Tarek, and Romy
('bob_clerk_user_id_placeholder', 'super-admin'), -- Bob is super-admin
('tarek_clerk_user_id_placeholder', 'manager'), -- Tarek is manager (change admin to manager)
('romy_clerk_user_id_placeholder', 'manager') -- Romy is manager (change admin to manager)
ON CONFLICT (user_id) DO NOTHING;

-- Now let's add all the contractors as employees
-- NOTE: We're excluding Bob Wazneh, Romy Hemadeh, and Tarek Hassoun as they're now admins

-- From Bob's CSV - People organized by their actual departments
INSERT INTO appy_employees (full_name, compensation, rate, department_id, manager_id, active) VALUES 
-- Operations Department
('Mona Bourgess', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Operations'), 'bob_clerk_user_id_placeholder', true),
('Phillip Alexeev', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Operations'), 'bob_clerk_user_id_placeholder', true),
('MK', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Operations'), 'bob_clerk_user_id_placeholder', true),
('Francesco Paolo Oddo', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Operations'), 'mona_manager_id', true),
('Mia Owaini', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Operations'), 'mona_manager_id', true),
('Gian Carlo U. Calixto', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Operations'), 'mona_manager_id', true),
('Mariangelica Angulo', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Operations'), 'mona_manager_id', true),
('Alexandra Narain Valero Hernandez', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Operations'), 'mia_manager_id', true),
('Ana Karina Superlano', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Operations'), 'mia_manager_id', true),
('Carla Aynoha Hernández Carrero', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Operations'), 'mia_manager_id', true),
('Liliany Villegas', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Operations'), 'mia_manager_id', true),
('Maria gabriela morales Gonzalez', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Operations'), 'mia_manager_id', true),
('Mariana Ordonez', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Operations'), 'mia_manager_id', true),
('Vanessa Vergara', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Operations'), 'mia_manager_id', true),
('Neiry Anais Padilla Osorio', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Operations'), 'mia_manager_id', true),
('Syed Mustafa Hasan', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Operations'), 'mia_manager_id', true),
('Janet Bahouth', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Operations'), 'marian_manager_id', true),
('Jayvee Mosquito Senining', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Operations'), 'marian_manager_id', true),
('Keith Adriel Abarca', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Operations'), 'marian_manager_id', true),
('Kenjay Senining', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Operations'), 'marian_manager_id', true),
('Muhammad Harris Tauseef', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Operations'), 'marian_manager_id', true),
('Muhammad Saad Farrukh', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Operations'), 'marian_manager_id', true),
('Natalie Roache', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Operations'), 'marian_manager_id', true),
('Ramzy Georges Khaled', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Operations'), 'marian_manager_id', true),
('Alberto Velandia', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Operations'), 'marian_manager_id', true),
('Luisa Andreina Del Villar Zerpa', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Operations'), 'marian_manager_id', true),
('Luisiana Alejandra Escalona Rodríguez', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Operations'), 'marian_manager_id', true),
('Jairus Gaite', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Operations'), 'bob_clerk_user_id_placeholder', true),
('Althene Kay Foliente', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Operations'), 'mona_manager_id', true),
('Angela Criselda H. Sison', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Operations'), 'mona_manager_id', true),
('Hiba Zeineddine', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Operations'), 'mona_manager_id', true),
('Issa Abou Daher', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Operations'), 'bob_clerk_user_id_placeholder', true),
('Tony Jean Abi Daoud', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Operations'), 'mona_manager_id', true),
('Joey Hourany', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Operations'), 'bob_clerk_user_id_placeholder', true),
('Mazen Khaled Noureddine', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Operations'), 'joey_manager_id', true),
('Charbel Dahan', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Operations'), 'joey_manager_id', true),
('Miguel Miled Khoury', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Operations'), 'joey_manager_id', true),
('Prash Poovaiah', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Operations'), 'joey_manager_id', true),
('Sally Kordab', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Operations'), 'joey_manager_id', true),
('Tarek Mohammad Mehdi', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Operations'), 'joey_manager_id', true),
('Zoey Fakhoury', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Operations'), 'joey_manager_id', true),
('Hanine Khalaf', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Operations'), 'natalie_manager_id', true),
-- Marketing Department
('Natalie Saginashvili', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Marketing'), 'bob_clerk_user_id_placeholder', true),
('Cherry Bell Chuca', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Marketing'), 'natalie_manager_id', true),
('Andalynne Michaels', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Marketing'), 'natalie_manager_id', true),
('Austin Flaugh', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Marketing'), 'natalie_manager_id', true),
('Jaalyn Andrea Tran', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Marketing'), 'natalie_manager_id', true),
('Lawal Abdullahi Abba', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Marketing'), 'natalie_manager_id', true),
('Alpay Aktug', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Marketing'), 'natalie_manager_id', true),
('Jose Ryan III Angeles', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Marketing'), 'natalie_manager_id', true),
('Bobby Zalke', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Marketing'), 'natalie_manager_id', true),
('Darko Petrovic', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Marketing'), 'natalie_manager_id', true),
('Francis Thomas Crevatas', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Marketing'), 'natalie_manager_id', true),
('Kunal Vijay Raut', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Marketing'), 'natalie_manager_id', true),
('Ma Ella Aragon', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Marketing'), 'natalie_manager_id', true),
('Mariano Herrera', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Marketing'), 'natalie_manager_id', true),
('Miguel Sarasua Dominguez', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Marketing'), 'natalie_manager_id', true),
('Mustafa Akkaş', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Marketing'), 'natalie_manager_id', true),
('Natalia Pimonova', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Marketing'), 'natalie_manager_id', true),
('Odejide Olusegun David', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Marketing'), 'natalie_manager_id', true),
('Oladokun Opeyemi Oladayo', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Marketing'), 'natalie_manager_id', true),
('Paula Brukaite', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Marketing'), 'natalie_manager_id', true),
('Venice Abegail Orit', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Marketing'), 'natalie_manager_id', true),
('Visar Hadri', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Marketing'), 'natalie_manager_id', true),
('Gaukhar Talgat Kyzy', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Marketing'), 'natalie_manager_id', true),
('Tarek Mohammad Hammoud', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Marketing'), 'natalie_manager_id', true),
-- Sales Department
('Ned Nedyalkov', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Sales'), 'bob_clerk_user_id_placeholder', true),
('Martin Rokicki', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Sales'), 'ned_manager_id', true),
('Ani Mildiani', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Sales'), 'ned_manager_id', true),
('Carlos Guilhon', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Sales'), 'ned_manager_id', true),
('Chris Orza', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Sales'), 'ned_manager_id', true),
('Eric Plaut', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Sales'), 'ned_manager_id', true),
('Gabriel Sari', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Sales'), 'ned_manager_id', true),
('Janis Madisson', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Sales'), 'ned_manager_id', true),
('Joseph Bigelli', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Sales'), 'ned_manager_id', true),
('Lyndon Brookes', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Sales'), 'ned_manager_id', true),
('Mayank Arora', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Sales'), 'ned_manager_id', true),
('Oliver Tervit', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Sales'), 'ned_manager_id', true),
('Dhirendra Pratap Singh', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Sales'), 'ned_manager_id', true),
('Sandeep Dewansingh', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Sales'), 'ned_manager_id', true),
('Talha', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Sales'), 'ned_manager_id', true),
('Saif', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Sales'), 'ned_manager_id', true);

-- From Romy's CSV (Media Department) - EXCLUDING Romy Hemadeh
INSERT INTO appy_employees (full_name, compensation, rate, department_id, manager_id, active) VALUES 
('Jessica Hasson', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'romy_clerk_user_id_placeholder', true),
('Cameron Penkauskas', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'jessica_manager_id', true),
('Daniel Khon-Perez', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'jessica_manager_id', true),
('Davi Nonato Braid', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'jessica_manager_id', true),
('Edwin DavidBeaty Marlowe', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'jessica_manager_id', true),
('Georgio Kozhaya Richa', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'jessica_manager_id', true),
('Ian Stuart Povey', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'jessica_manager_id', true),
('Janne Nel', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'jessica_manager_id', true),
('Javaid Ahmad Shah', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'jessica_manager_id', true),
('Jean-Paul Jean Bitar', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'jessica_manager_id', true),
('Mhedi Banafshei Kohneh Ferod', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'jessica_manager_id', true),
('Oliver Clifford Anderson', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'jessica_manager_id', true),
('Panagiota Fragkaki', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'jessica_manager_id', true),
('Claudio Umberto Antonio Aresu', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'jessica_manager_id', true),
('Candela Sol Silva', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'jessica_manager_id', true),
('Charbel Antoun', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'jessica_manager_id', true),
('Ziad El Bitar', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'jessica_manager_id', true),
('Oliver Chartouni', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'jessica_manager_id', true),
('Nik Hribar', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'jessica_manager_id', true),
('Annette Nel', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'romy_clerk_user_id_placeholder', true),
('Rianna de Bono-Smith', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'romy_clerk_user_id_placeholder', true),
('Sergio Velasco Huertas', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'romy_clerk_user_id_placeholder', true),
('Irfan Ali Qureshi', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'romy_clerk_user_id_placeholder', true),
('Joya Baradii', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'romy_clerk_user_id_placeholder', true),
('Malique Daniels', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'romy_clerk_user_id_placeholder', true),
('Robert Jacobson', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'romy_clerk_user_id_placeholder', true),
('Scott Ratner', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'romy_clerk_user_id_placeholder', true),
('Rahul Khurana', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'romy_clerk_user_id_placeholder', true),
('Greg Tafranian', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'romy_clerk_user_id_placeholder', true),
('Luke John Mathias', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'romy_clerk_user_id_placeholder', true),
('Gabryela Giglio Rogerio', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'romy_clerk_user_id_placeholder', true),
('Danielle Akkawi', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'romy_clerk_user_id_placeholder', true),
('Dyhego Sihao Cardoso', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'romy_clerk_user_id_placeholder', true),
('Assadour Darakjian', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'romy_clerk_user_id_placeholder', true),
('Sophia Leme Matos', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'romy_clerk_user_id_placeholder', true),
('Hannah Hughes', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'romy_clerk_user_id_placeholder', true),
('Allan Pleasanton', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'romy_clerk_user_id_placeholder', true),
('Connor Luke Browne', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'romy_clerk_user_id_placeholder', true),
('Pavandeep Singh Paul', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'romy_clerk_user_id_placeholder', true),
('Sarah Patillo', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'romy_clerk_user_id_placeholder', true),
('Travis Wright', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'romy_clerk_user_id_placeholder', true),
('Danish Nagda', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'romy_clerk_user_id_placeholder', true),
('David Scott Pollack', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'romy_clerk_user_id_placeholder', true),
('Drew Otis Weidert', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'romy_clerk_user_id_placeholder', true),
('Roger Caneda', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'romy_clerk_user_id_placeholder', true),
('Shane Patrick Cook', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'romy_clerk_user_id_placeholder', true),
('Joe Sayegh', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'romy_clerk_user_id_placeholder', true),
('Adrian Alonso Borjas Jerez', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'romy_clerk_user_id_placeholder', true),
('Dawn Marie France Ayrout', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'romy_clerk_user_id_placeholder', true),
('Louloua Bou Khaled', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'romy_clerk_user_id_placeholder', true),
('Melissa Bou Harb', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'romy_clerk_user_id_placeholder', true),
('Rita Abi Daoud', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'romy_clerk_user_id_placeholder', true),
('Elina Nassif', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'romy_clerk_user_id_placeholder', true),
('Jade Elliott', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'romy_clerk_user_id_placeholder', true),
('Michael Allen Barber', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'romy_clerk_user_id_placeholder', true),
('Michael C. Tracey', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'romy_clerk_user_id_placeholder', true),
('Neil Francis Degamo', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'romy_clerk_user_id_placeholder', true),
('Ricardo Andrade', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'romy_clerk_user_id_placeholder', true),
('Roshini Hussain', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'romy_clerk_user_id_placeholder', true),
('Sarine Mihranian', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'romy_clerk_user_id_placeholder', true),
('Tracey Firman', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'romy_clerk_user_id_placeholder', true),
('Anastasiia Pivchenko', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'joey_manager_id', true),
('Heitor Villa Verde Revelles Pereira', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Media'), 'joey_manager_id', true),
('Burak Ozturk', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Executive'), 'mario_manager_id', true),
('Nina Katharina Laue', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Executive'), 'mario_manager_id', true);

-- From Tarek's CSV (Finance/Legal Department) - EXCLUDING Tarek Hassoun
INSERT INTO appy_employees (full_name, compensation, rate, department_id, manager_id, active) VALUES 
('Giorgi Natroshvili', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Finance'), 'tarek_clerk_user_id_placeholder', true),
('Joseph Nemer', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Finance'), 'tarek_clerk_user_id_placeholder', true),
('Elias Nassif', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Finance'), 'tarek_clerk_user_id_placeholder', true),
('Khodr Hassoun', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Finance'), 'tarek_clerk_user_id_placeholder', true),
('Lynn El Ahmar', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Finance'), 'tarek_clerk_user_id_placeholder', true),
('Patrick Assaf', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Finance'), 'joseph_manager_id', true),
('Tracy Rahmeh', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Finance'), 'joseph_manager_id', true),
('John Cloude Chamandi', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Finance'), 'joseph_manager_id', true),
('Daniel Dimov', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Legal'), 'tarek_clerk_user_id_placeholder', true),
('Eliane Badro', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Legal'), 'tarek_clerk_user_id_placeholder', true),
('Joenie Villarino Jr', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Legal'), 'tarek_clerk_user_id_placeholder', true),
('Meryll Joyce Laygo Ferma', 0, 'monthly', (SELECT id FROM appy_departments WHERE name = 'Legal'), 'tarek_clerk_user_id_placeholder', true);

-- Create an active appraisal period (from 02-seed.sql)
INSERT INTO appy_appraisal_periods (name, start_date, end_date)
VALUES ('Current Period', date_trunc('month', current_date), date_trunc('month', current_date) + interval '1 month - 1 day')
ON CONFLICT DO NOTHING;

COMMIT;