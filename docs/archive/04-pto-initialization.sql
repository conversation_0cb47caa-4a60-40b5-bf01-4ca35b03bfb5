-- Initialize PTO balances for Marketing department
-- This script sets up initial PTO balances for all Marketing department employees
-- 
-- PREREQUISITES: 
-- 1. Run 01-schema.sql to create base tables
-- 2. Run 02-seed.sql or contractors.sql to populate employees  
-- 3. Run 03-pto-schema.sql to create PTO tables
-- 
-- USAGE:
-- This script should be run after the PTO schema is created and employees are populated

-- First, let's check if we have Marketing department employees
DO $$
DECLARE
  marketing_count INTEGER;
  current_year INTEGER := EXTRACT(YEAR FROM CURRENT_DATE);
  rec RECORD;
BEGIN
  -- Count Marketing department employees
  SELECT COUNT(*) INTO marketing_count
  FROM appy_employees e
  JOIN appy_departments d ON e.department_id = d.id
  WHERE d.name = 'Marketing' AND e.active = true;
  
  RAISE NOTICE 'Found % Marketing department employees', marketing_count;
  
  -- Only proceed if we have Marketing employees
  IF marketing_count > 0 THEN
    -- Initialize PTO balances for Marketing department employees
    INSERT INTO appy_employee_pto_balances (employee_id, year, total_days, used_days)
    SELECT 
      e.id,
      current_year,
      7, -- 7 days PTO per year
      0  -- Start with 0 used days
    FROM appy_employees e
    JOIN appy_departments d ON e.department_id = d.id
    WHERE d.name = 'Marketing' 
      AND e.active = true
    ON CONFLICT (employee_id, year) DO UPDATE SET
      total_days = EXCLUDED.total_days,
      updated_at = now();
    
    -- Log the initialization
    RAISE NOTICE 'Initialized PTO balances for % Marketing employees for year %', marketing_count, current_year;
    
    -- Show the results
    RAISE NOTICE 'Marketing Department PTO Balances:';
    
    -- Display the created balances
    FOR rec IN 
      SELECT 
        e.full_name,
        b.total_days,
        b.used_days,
        b.available_days,
        b.year
      FROM appy_employee_pto_balances b
      JOIN appy_employees e ON b.employee_id = e.id
      JOIN appy_departments d ON e.department_id = d.id
      WHERE d.name = 'Marketing' 
        AND b.year = current_year
      ORDER BY e.full_name
    LOOP
      RAISE NOTICE '  % - Total: % days, Used: % days, Available: % days (%)', 
        rec.full_name, rec.total_days, rec.used_days, rec.available_days, rec.year;
    END LOOP;
    
  ELSE
    RAISE NOTICE 'No Marketing department employees found. Please ensure:';
    RAISE NOTICE '1. The Marketing department exists in appy_departments';
    RAISE NOTICE '2. Employees are assigned to the Marketing department';
    RAISE NOTICE '3. Employees are marked as active';
  END IF;
END $$;

-- Verify the initialization worked
SELECT 
  d.name as department_name,
  COUNT(b.id) as employees_with_pto,
  AVG(b.total_days) as avg_total_days,
  SUM(b.used_days) as total_used_days,
  SUM(b.available_days) as total_available_days
FROM appy_employee_pto_balances b
JOIN appy_employees e ON b.employee_id = e.id
JOIN appy_departments d ON e.department_id = d.id
WHERE d.name = 'Marketing'
  AND b.year = EXTRACT(YEAR FROM CURRENT_DATE)
GROUP BY d.name;

-- Show individual balances for verification
SELECT 
  e.full_name as employee_name,
  d.name as department_name,
  b.year,
  b.total_days,
  b.used_days,
  b.available_days,
  b.created_at,
  b.updated_at
FROM appy_employee_pto_balances b
JOIN appy_employees e ON b.employee_id = e.id
JOIN appy_departments d ON e.department_id = d.id
WHERE d.name = 'Marketing'
  AND b.year = EXTRACT(YEAR FROM CURRENT_DATE)
ORDER BY e.full_name;

-- Optional: Create a sample PTO request for testing
-- Uncomment the following section if you want to create a test PTO request

/*
-- Create a sample PTO request for testing (only if Natalie exists as a manager)
DO $$
DECLARE
  natalie_id TEXT;
  sample_employee_id UUID;
  sample_request_id UUID;
BEGIN
  -- Find Natalie as manager
  SELECT user_id INTO natalie_id
  FROM appy_managers
  WHERE full_name ILIKE '%Natalie%'
  LIMIT 1;
  
  -- Find a Marketing employee
  SELECT e.id INTO sample_employee_id
  FROM appy_employees e
  JOIN appy_departments d ON e.department_id = d.id
  WHERE d.name = 'Marketing'
    AND e.active = true
  LIMIT 1;
  
  -- Create a sample PTO request if both exist
  IF natalie_id IS NOT NULL AND sample_employee_id IS NOT NULL THEN
    INSERT INTO appy_pto_requests (
      employee_id,
      manager_id,
      request_type,
      start_date,
      end_date,
      days_requested,
      reason,
      status
    ) VALUES (
      sample_employee_id,
      natalie_id,
      'vacation',
      CURRENT_DATE + INTERVAL '7 days',
      CURRENT_DATE + INTERVAL '8 days',
      2,
      'Sample PTO request for testing the system',
      'pending'
    ) RETURNING id INTO sample_request_id;
    
    RAISE NOTICE 'Created sample PTO request with ID: %', sample_request_id;
  ELSE
    RAISE NOTICE 'Could not create sample PTO request - missing Natalie or Marketing employee';
  END IF;
END $$;
*/

COMMIT;