-- Update all table references to use appy_ prefix
-- Run this after creating the schema with appy_ prefixed tables

-- This file shows the mapping from old to new table names:
-- departments → appy_departments
-- employees → appy_employees  
-- managers → appy_managers
-- user_roles → appy_user_roles
-- appraisal_periods → appy_appraisal_periods
-- appraisals → appy_appraisals
-- employee_assignments → (removed - manager_id is now directly on appy_employees)