[{"table_name": "appy_appraisal_periods", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "gen_random_uuid()"}, {"table_name": "appy_appraisal_periods", "column_name": "name", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "appy_appraisal_periods", "column_name": "start_date", "data_type": "date", "is_nullable": "NO", "column_default": null}, {"table_name": "appy_appraisal_periods", "column_name": "end_date", "data_type": "date", "is_nullable": "NO", "column_default": null}, {"table_name": "appy_appraisal_periods", "column_name": "status", "data_type": "text", "is_nullable": "YES", "column_default": "'draft'::text"}, {"table_name": "appy_appraisal_periods", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_name": "appy_appraisals", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "gen_random_uuid()"}, {"table_name": "appy_appraisals", "column_name": "employee_id", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"table_name": "appy_appraisals", "column_name": "period_id", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"table_name": "appy_appraisals", "column_name": "manager_id", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "appy_appraisals", "column_name": "question_1", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "appy_appraisals", "column_name": "question_2", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "appy_appraisals", "column_name": "question_3", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "appy_appraisals", "column_name": "question_4", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "appy_appraisals", "column_name": "question_5", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "appy_appraisals", "column_name": "status", "data_type": "USER-DEFINED", "is_nullable": "YES", "column_default": "'pending'::appy_appraisal_status"}, {"table_name": "appy_appraisals", "column_name": "submitted_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"table_name": "appy_appraisals", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_name": "appy_departments", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "gen_random_uuid()"}, {"table_name": "appy_departments", "column_name": "name", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "appy_departments", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_name": "appy_employee_pto_balances", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "gen_random_uuid()"}, {"table_name": "appy_employee_pto_balances", "column_name": "employee_id", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"table_name": "appy_employee_pto_balances", "column_name": "year", "data_type": "integer", "is_nullable": "NO", "column_default": "EXTRACT(year FROM CURRENT_DATE)"}, {"table_name": "appy_employee_pto_balances", "column_name": "total_days", "data_type": "integer", "is_nullable": "NO", "column_default": "7"}, {"table_name": "appy_employee_pto_balances", "column_name": "used_days", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"table_name": "appy_employee_pto_balances", "column_name": "available_days", "data_type": "integer", "is_nullable": "YES", "column_default": null}, {"table_name": "appy_employee_pto_balances", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_name": "appy_employee_pto_balances", "column_name": "updated_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_name": "appy_employees", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "gen_random_uuid()"}, {"table_name": "appy_employees", "column_name": "full_name", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "appy_employees", "column_name": "compensation", "data_type": "numeric", "is_nullable": "NO", "column_default": null}, {"table_name": "appy_employees", "column_name": "rate", "data_type": "USER-DEFINED", "is_nullable": "NO", "column_default": null}, {"table_name": "appy_employees", "column_name": "department_id", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"table_name": "appy_employees", "column_name": "manager_id", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "appy_employees", "column_name": "active", "data_type": "boolean", "is_nullable": "YES", "column_default": "true"}, {"table_name": "appy_employees", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_name": "appy_managers", "column_name": "user_id", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "appy_managers", "column_name": "full_name", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "appy_managers", "column_name": "email", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "appy_managers", "column_name": "department_id", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"table_name": "appy_managers", "column_name": "active", "data_type": "boolean", "is_nullable": "YES", "column_default": "true"}, {"table_name": "appy_managers", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_name": "appy_pto_requests", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "gen_random_uuid()"}, {"table_name": "appy_pto_requests", "column_name": "employee_id", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"table_name": "appy_pto_requests", "column_name": "manager_id", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "appy_pto_requests", "column_name": "request_type", "data_type": "USER-DEFINED", "is_nullable": "NO", "column_default": "'vacation'::appy_pto_request_type"}, {"table_name": "appy_pto_requests", "column_name": "start_date", "data_type": "date", "is_nullable": "NO", "column_default": null}, {"table_name": "appy_pto_requests", "column_name": "end_date", "data_type": "date", "is_nullable": "NO", "column_default": null}, {"table_name": "appy_pto_requests", "column_name": "days_requested", "data_type": "integer", "is_nullable": "NO", "column_default": null}, {"table_name": "appy_pto_requests", "column_name": "reason", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "appy_pto_requests", "column_name": "status", "data_type": "USER-DEFINED", "is_nullable": "NO", "column_default": "'pending'::appy_pto_status"}, {"table_name": "appy_pto_requests", "column_name": "approved_by", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "appy_pto_requests", "column_name": "approved_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"table_name": "appy_pto_requests", "column_name": "rejected_reason", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "appy_pto_requests", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_name": "appy_pto_requests", "column_name": "updated_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_name": "appy_user_roles", "column_name": "user_id", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "appy_user_roles", "column_name": "role", "data_type": "USER-DEFINED", "is_nullable": "NO", "column_default": null}]