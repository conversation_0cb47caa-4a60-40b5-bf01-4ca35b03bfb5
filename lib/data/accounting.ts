import type { AccountingViewData, Employee } from '../types'
import { db } from '../db'
import { getEmployees } from './employees'
import { getPeriods } from './periods'

export async function getAccountingData(): Promise<AccountingViewData[]> {
  try {
    const employees = await getEmployees()
    // TODO: In real implementation, this would join with appraisals table to get actual status and submission times
    return employees.map((emp) => ({
      employeeId: emp.id,
      employeeName: emp.fullName,
      departmentName: emp.departmentName!,
      managerName: emp.managerName || 'Unassigned',
      status: "not-started" as const, // In real app, this would come from appraisals table
      submittedAt: null, // In real app, this would come from appraisals table
      compensation: emp.compensation,
      rate: emp.rate,
      hours: emp.compensation === "hourly" ? 160 : 160, // Standard work hours
    }))
  } catch (error) {
    console.error('Failed to fetch accounting data:', error)
    return []
  }
}

export async function getAccountingDataForUser(): Promise<AccountingViewData[]> {
  try {
    const { getCurrentUser } = await import('../auth')
    const currentUser = await getCurrentUser()
    
    if (!currentUser) {
      console.error('No authenticated user found')
      return []
    }
    
    // Get current active period
    const periods = await getPeriods()
    const currentPeriod = periods.find(p => !p.closed)
    
    if (!currentPeriod) {
      console.warn('No active appraisal period found')
      return []
    }
    
    // Get all employees first
    const employees = await getEmployees()
    
    // Filter based on user role
    let filteredEmployees: Employee[]
    
    if (currentUser.role === 'super-admin') {
      // Super admins see all employees
      filteredEmployees = employees
    } else if (currentUser.role === 'accountant') {
      // Accountants see all employees (for payroll processing)
      filteredEmployees = employees
    } else if (currentUser.role === 'manager') {
      // Managers only see employees under their supervision
      filteredEmployees = employees.filter(emp => emp.managerId === currentUser.id)
    } else if (currentUser.role === 'hr-admin' || currentUser.role === 'admin') {
      // HR admins and admins see all employees
      filteredEmployees = employees
    } else {
      // Default: no access
      filteredEmployees = []
    }
    
    // Get appraisals for the current period
    const appraisals = await db.getAppraisalsWithEmployeeData(currentPeriod.id)
    
    // Create a map of employee ID to appraisal data
    const appraisalMap = new Map(appraisals.map(appraisal => [appraisal.employee_id, appraisal]))
    
    // Build the accounting view data with real appraisal statuses
    return filteredEmployees.map((emp) => {
      const appraisal = appraisalMap.get(emp.id)
      
      // Map database status to UI status
      let status: 'not-started' | 'draft' | 'submitted' = 'not-started'
      let submittedAt: string | null = null
      
      if (appraisal) {
        if (appraisal.status === 'submitted') {
          status = 'submitted'
          submittedAt = appraisal.submitted_at
        } else if (appraisal.status === 'pending') {
          status = 'draft'
        }
      }
      
      return {
        employeeId: emp.id,
        employeeName: emp.fullName,
        departmentName: emp.departmentName!,
        managerName: emp.managerName || 'Unassigned',
        status,
        submittedAt,
        compensation: emp.compensation,
        rate: emp.rate,
        hours: emp.compensation === "hourly" ? 160 : 160, // Standard work hours
      }
    })
  } catch (error) {
    console.error('Failed to fetch accounting data for user:', error)
    return []
  }
}